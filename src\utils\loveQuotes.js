// Beautiful love quotes for inspiration
export const loveQuotes = [
  {
    text: "You are my today and all of my tomorrows.",
    author: "<PERSON>"
  },
  {
    text: "In all the world, there is no heart for me like yours. In all the world, there is no love for you like mine.",
    author: "<PERSON>"
  },
  {
    text: "I have found the one whom my soul loves.",
    author: "Song of Solomon 3:4"
  },
  {
    text: "You are my sun, my moon, and all my stars.",
    author: "<PERSON><PERSON><PERSON><PERSON>"
  },
  {
    text: "I love you not only for what you are, but for what I am when I am with you.",
    author: "<PERSON>"
  },
  {
    text: "Every love story is beautiful, but ours is my favorite.",
    author: "Unknown"
  },
  {
    text: "You are the finest, loveliest, tenderest, and most beautiful person I have ever known.",
    author: "<PERSON><PERSON>"
  },
  {
    text: "I choose you. And I'll choose you over and over and over. Without pause, without a doubt, in a heartbeat. I'll keep choosing you.",
    author: "Unknown"
  },
  {
    text: "You are my heart, my life, my one and only thought.",
    author: "<PERSON>"
  },
  {
    text: "I fell in love the way you fall asleep: slowly, and then all at once.",
    author: "<PERSON>"
  },
  {
    text: "You make me want to be a better person.",
    author: "As Good as It Gets"
  },
  {
    text: "I love you more than yesterday, but less than tomorrow.",
    author: "<PERSON> Rostand"
  },
  {
    text: "You are my greatest adventure.",
    author: "The Incredibles"
  },
  {
    text: "I would rather spend one lifetime with you, than face all the ages of this world alone.",
    author: "J.R.R. Tolkien"
  },
  {
    text: "You are the source of my joy, the center of my world and the whole of my heart.",
    author: "Unknown"
  },
  {
    text: "I love you to the moon and back.",
    author: "Sam McBratney"
  },
  {
    text: "You are my person.",
    author: "Grey's Anatomy"
  },
  {
    text: "I love you more than words can express and more than actions can show.",
    author: "Unknown"
  },
  {
    text: "You are the love that came without warning; you had my heart before I could say no.",
    author: "Unknown"
  },
  {
    text: "I love you not because of who you are, but because of who I am when I am with you.",
    author: "Roy Croft"
  }
];

// Get a random love quote
export const getRandomLoveQuote = () => {
  const randomIndex = Math.floor(Math.random() * loveQuotes.length);
  return loveQuotes[randomIndex];
};

// Get multiple random quotes
export const getRandomLoveQuotes = (count = 3) => {
  const shuffled = [...loveQuotes].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

// Love message templates for inspiration
export const loveMessageTemplates = [
  "My dearest {partnerName}, every moment with you feels like a beautiful dream. You are my sunshine on cloudy days and my anchor in stormy seas. I love you more than words can express.",
  
  "To my beloved {partnerName}, you have filled my life with colors I never knew existed. Your smile lights up my world, and your love gives me strength. Forever and always, you are my everything.",
  
  "Sweet {partnerName}, from the moment I met you, I knew my life would never be the same. You are my best friend, my soulmate, and my greatest love. Thank you for being you.",
  
  "My love {partnerName}, you are the melody to my heart's song, the missing piece to my puzzle, and the answer to my prayers. I am so grateful to have you in my life.",
  
  "Darling {partnerName}, with you, I have found my home. Your love is my safe haven, your arms my favorite place to be. I promise to love you with all my heart, today and always.",
  
  "To the love of my life, {partnerName}, you make ordinary moments extraordinary and turn simple days into magical adventures. I love you beyond measure.",
  
  "My precious {partnerName}, you are my today and all of my tomorrows. In your eyes, I see my future, and in your heart, I have found my home.",
  
  "Beautiful {partnerName}, you are the reason I believe in love, the inspiration behind my smiles, and the source of my happiness. I love you more each day."
];

// Get a random love message template
export const getRandomLoveMessageTemplate = (partnerName = "my love") => {
  const randomIndex = Math.floor(Math.random() * loveMessageTemplates.length);
  const template = loveMessageTemplates[randomIndex];
  return template.replace(/{partnerName}/g, partnerName);
};

// Romantic emojis for decoration
export const romanticEmojis = [
  "💖", "💕", "💗", "💓", "💝", "💘", "💞", "💟",
  "❤️", "🧡", "💛", "💚", "💙", "💜", "🤍", "🖤",
  "💐", "🌹", "🌺", "🌸", "🌼", "🌻", "🌷", "🥀",
  "✨", "⭐", "🌟", "💫", "🌙", "☀️", "🌈", "🦋",
  "👑", "💎", "🎀", "🎁", "🍾", "🥂", "🍰", "🎂"
];

// Get random romantic emojis
export const getRandomRomanticEmojis = (count = 5) => {
  const shuffled = [...romanticEmojis].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};
