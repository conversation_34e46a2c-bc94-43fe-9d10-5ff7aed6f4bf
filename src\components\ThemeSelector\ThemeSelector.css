.theme-selector {
  position: fixed;
  top: var(--space-6);
  right: var(--space-6);
  z-index: 100;
}

/* Theme Toggle Button */
.theme-toggle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(26, 26, 58, 0.9);
  backdrop-filter: blur(10px);
  border: 2px solid var(--galaxy-gold);
  color: var(--galaxy-white);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-lg);
}

.theme-toggle:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-glow);
  background: rgba(26, 26, 58, 1);
}

.theme-preview {
  font-size: 1.2rem;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.theme-icon {
  width: 20px;
  height: 20px;
  stroke-width: 2;
  opacity: 0.7;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

/* Theme Panel */
.theme-panel {
  position: absolute;
  top: 60px;
  right: 0;
  width: 350px;
  background: rgba(26, 26, 58, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-xl);
  animation: slideInFromTop 0.3s ease-out;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.theme-header {
  text-align: center;
  margin-bottom: var(--space-6);
}

.theme-header h3 {
  margin: 0 0 var(--space-2) 0;
  color: var(--galaxy-gold);
  font-size: var(--font-size-lg);
  font-weight: 600;
}

.theme-header p {
  margin: 0;
  color: var(--galaxy-silver);
  font-size: var(--font-size-sm);
}

.theme-grid {
  display: grid;
  gap: var(--space-3);
  margin-bottom: var(--space-6);
}

.theme-option {
  background: rgba(26, 26, 58, 0.6);
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: var(--space-4);
  text-align: left;
}

.theme-option:hover {
  border-color: var(--galaxy-gold);
  background: rgba(26, 26, 58, 0.8);
  transform: translateY(-2px);
}

.theme-option.active {
  border-color: var(--galaxy-gold);
  background: rgba(255, 215, 0, 0.1);
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.theme-preview-large {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  flex-shrink: 0;
}

.theme-emoji {
  font-size: 1.5rem;
  display: block;
}

.theme-colors {
  display: flex;
  gap: 2px;
}

.color-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.theme-info {
  flex: 1;
}

.theme-info h4 {
  margin: 0 0 var(--space-1) 0;
  color: var(--galaxy-white);
  font-size: var(--font-size-sm);
  font-weight: 600;
}

.theme-info p {
  margin: 0;
  color: var(--galaxy-silver);
  font-size: var(--font-size-xs);
  line-height: 1.4;
}

.theme-selected {
  position: absolute;
  top: var(--space-2);
  right: var(--space-2);
  width: 20px;
  height: 20px;
  background: var(--galaxy-gold);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--galaxy-deep-blue);
}

.theme-selected svg {
  width: 12px;
  height: 12px;
  stroke-width: 3;
}

.theme-hover-effect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    var(--theme-accent, var(--galaxy-gold)) 0%, 
    transparent 50%, 
    var(--theme-accent, var(--galaxy-gold)) 100%);
  opacity: 0.1;
  pointer-events: none;
}

.theme-footer {
  text-align: center;
  padding-top: var(--space-4);
  border-top: 1px solid rgba(255, 215, 0, 0.2);
}

.theme-footer p {
  margin: 0;
  color: var(--galaxy-silver);
  font-size: var(--font-size-xs);
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .theme-selector {
    top: var(--space-4);
    right: var(--space-4);
  }
  
  .theme-toggle {
    width: 45px;
    height: 45px;
  }
  
  .theme-preview {
    font-size: 1rem;
  }
  
  .theme-icon {
    width: 18px;
    height: 18px;
  }
  
  .theme-panel {
    width: 300px;
    padding: var(--space-4);
  }
  
  .theme-option {
    padding: var(--space-3);
    gap: var(--space-3);
  }
  
  .theme-emoji {
    font-size: 1.2rem;
  }
  
  .color-dot {
    width: 6px;
    height: 6px;
  }
}

@media (max-width: 480px) {
  .theme-panel {
    width: 280px;
    right: -var(--space-4);
  }
  
  .theme-option {
    flex-direction: column;
    text-align: center;
    gap: var(--space-2);
  }
  
  .theme-preview-large {
    flex-direction: row;
    gap: var(--space-3);
  }
  
  .theme-info h4 {
    font-size: var(--font-size-xs);
  }
  
  .theme-info p {
    font-size: 10px;
  }
}

/* Accessibility */
.theme-toggle:focus,
.theme-option:focus {
  outline: 2px solid var(--galaxy-gold);
  outline-offset: 2px;
}

/* Performance optimizations */
.theme-panel {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
}

@media (prefers-reduced-motion: reduce) {
  .theme-panel {
    animation: none !important;
  }
  
  .theme-toggle:hover,
  .theme-option:hover {
    transform: none !important;
  }
}
