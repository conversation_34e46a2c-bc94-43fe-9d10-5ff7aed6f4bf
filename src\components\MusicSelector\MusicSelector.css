/* Music Selector Styles */
.music-selector {
  margin-bottom: var(--space-4);
}

/* Current Music Display */
.current-music {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
}

.music-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.music-icon {
  font-size: var(--font-size-xl);
  animation: musicPulse 2s ease-in-out infinite;
}

.music-details {
  display: flex;
  flex-direction: column;
}

.music-name {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  font-size: var(--font-size-base);
}

.music-artist {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.music-actions {
  display: flex;
  gap: var(--space-2);
}

/* Music Placeholder */
.music-placeholder {
  padding: var(--space-4);
}

.btn-icon {
  margin-right: var(--space-2);
}

/* Modal Styles */
.music-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  animation: fadeIn 0.3s ease;
}

.music-modal {
  background: var(--bg-primary);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: var(--radius-xl);
  width: 90vw;
  max-width: 800px;
  max-height: 80vh;
  overflow: hidden;
  animation: slideInUp 0.3s ease;
  box-shadow: var(--shadow-2xl);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-6);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 215, 0, 0.1);
}

.modal-header h3 {
  margin: 0;
  color: var(--galaxy-gold);
  font-family: 'Dancing Script', cursive;
  font-size: var(--font-size-xl);
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-2xl);
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.modal-close:hover {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.1);
}

.modal-content {
  padding: var(--space-6);
  overflow-y: auto;
  max-height: calc(80vh - 100px);
}

/* Search Section */
.search-section {
  margin-bottom: var(--space-6);
}

.search-input {
  position: relative;
}

.search-input input {
  width: 100%;
  padding-right: var(--space-10);
}

.search-icon {
  position: absolute;
  right: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  pointer-events: none;
}

/* Categories Section */
.categories-section {
  margin-bottom: var(--space-6);
}

.categories-list {
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
}

.category-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-size: var(--font-size-sm);
}

.category-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  transform: translateY(-2px);
}

.category-btn.active {
  background: rgba(255, 215, 0, 0.2);
  border-color: var(--galaxy-gold);
  color: var(--galaxy-gold);
}

.category-icon {
  font-size: var(--font-size-base);
}

/* Songs Section */
.songs-section {
  max-height: 400px;
  overflow-y: auto;
}

.songs-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.song-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
}

.song-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 215, 0, 0.3);
  transform: translateY(-2px);
}

.song-info {
  flex: 1;
  min-width: 0;
}

.song-main {
  margin-bottom: var(--space-2);
}

.song-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-1) 0;
}

.song-artist {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0 0 var(--space-1) 0;
}

.song-description {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  margin: 0;
  font-style: italic;
}

.song-meta {
  display: flex;
  gap: var(--space-4);
  align-items: center;
  flex-wrap: wrap;
}

.song-duration,
.song-mood,
.song-genre {
  font-size: var(--font-size-xs);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  background: rgba(255, 255, 255, 0.1);
}

.song-mood {
  font-weight: var(--font-weight-medium);
}

.song-actions {
  display: flex;
  gap: var(--space-2);
  margin-left: var(--space-4);
}

.no-songs {
  text-align: center;
  padding: var(--space-8);
  color: var(--text-muted);
}

/* Animations */
@keyframes musicPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .music-modal {
    width: 95vw;
    max-height: 90vh;
  }
  
  .modal-content {
    padding: var(--space-4);
  }
  
  .categories-list {
    justify-content: center;
  }
  
  .song-item {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
  }
  
  .song-actions {
    margin-left: 0;
    justify-content: center;
  }
  
  .song-meta {
    justify-content: center;
  }
  
  .current-music {
    flex-direction: column;
    gap: var(--space-3);
    text-align: center;
  }
  
  .music-actions {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .modal-header {
    padding: var(--space-4);
  }
  
  .modal-header h3 {
    font-size: var(--font-size-lg);
  }
  
  .categories-list {
    flex-direction: column;
  }
  
  .category-btn {
    justify-content: center;
  }
}
