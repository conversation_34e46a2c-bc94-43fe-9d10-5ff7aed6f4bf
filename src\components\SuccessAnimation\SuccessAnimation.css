.success-animation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--galaxy-deep-blue), var(--galaxy-dark-blue));
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.success-content {
  text-align: center;
  position: relative;
  z-index: 1;
  max-width: 500px;
  padding: var(--space-8);
}

/* Success Icon */
.success-icon {
  margin-bottom: var(--space-8);
  opacity: 0;
  transform: scale(0);
  transition: all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.success-icon.animate {
  opacity: 1;
  transform: scale(1);
}

.success-circle {
  width: 120px;
  height: 120px;
  margin: 0 auto;
  background: linear-gradient(135deg, var(--galaxy-gold), #ffed4e);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--galaxy-deep-blue);
  box-shadow: 0 0 40px rgba(255, 215, 0, 0.6);
  animation: successPulse 2s ease-in-out infinite;
  position: relative;
}

.success-circle::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: linear-gradient(135deg, var(--galaxy-gold), #ffed4e);
  border-radius: 50%;
  opacity: 0.3;
  animation: successRipple 2s ease-out infinite;
}

.success-circle svg {
  width: 60px;
  height: 60px;
  stroke-width: 3;
  animation: successCheck 1s ease-out 0.5s both;
}

@keyframes successPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 40px rgba(255, 215, 0, 0.6);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 60px rgba(255, 215, 0, 0.8);
  }
}

@keyframes successRipple {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

@keyframes successCheck {
  0% {
    stroke-dasharray: 0 100;
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dasharray: 100 100;
    stroke-dashoffset: 0;
  }
}

/* Success Message */
.success-message {
  margin-bottom: var(--space-8);
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out;
}

.success-message.animate {
  opacity: 1;
  transform: translateY(0);
}

.success-message h2 {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  color: var(--galaxy-gold);
  margin-bottom: var(--space-4);
  font-weight: 700;
  text-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.success-message p {
  font-size: var(--font-size-lg);
  color: var(--galaxy-silver);
  margin: 0;
}

/* Floating Hearts */
.success-hearts {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.5s ease-out;
}

.success-hearts.animate {
  opacity: 1;
}

.success-heart {
  position: absolute;
  font-size: 1.5rem;
  animation: successHeartFloat 4s ease-out infinite;
}

@keyframes successHeartFloat {
  0% {
    opacity: 0;
    transform: translateY(100vh) scale(0) rotate(0deg);
  }
  15% {
    opacity: 1;
    transform: translateY(80vh) scale(1) rotate(10deg);
  }
  85% {
    opacity: 1;
    transform: translateY(20vh) scale(1.2) rotate(-10deg);
  }
  100% {
    opacity: 0;
    transform: translateY(-20vh) scale(0) rotate(20deg);
  }
}

/* Sparkle Effects */
.success-sparkles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.5s ease-out;
}

.success-sparkles.animate {
  opacity: 1;
}

.success-sparkle {
  position: absolute;
  font-size: 1rem;
  animation: successSparkle 2s ease-in-out infinite;
}

@keyframes successSparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.5) rotate(180deg);
  }
}

/* Confetti */
.success-confetti {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.5s ease-out;
}

.success-confetti.animate {
  opacity: 1;
}

.success-confetti-piece {
  position: absolute;
  width: 6px;
  height: 6px;
  animation: successConfetti linear infinite;
}

@keyframes successConfetti {
  0% {
    opacity: 1;
    transform: translateY(-100vh) rotate(0deg);
  }
  100% {
    opacity: 0;
    transform: translateY(100vh) rotate(720deg);
  }
}

/* Loading Dots */
.success-loading {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.5s ease-out;
}

.success-loading.animate {
  opacity: 1;
  transform: translateY(0);
}

.success-loading p {
  color: var(--galaxy-white);
  font-size: var(--font-size-base);
  margin-bottom: var(--space-4);
}

.loading-dots {
  display: flex;
  justify-content: center;
  gap: var(--space-2);
}

.loading-dots span {
  width: 8px;
  height: 8px;
  background: var(--galaxy-gold);
  border-radius: 50%;
  animation: successDots 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes successDots {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .success-content {
    padding: var(--space-6);
  }
  
  .success-circle {
    width: 100px;
    height: 100px;
  }
  
  .success-circle svg {
    width: 50px;
    height: 50px;
  }
  
  .success-message h2 {
    font-size: 1.5rem;
  }
  
  .success-message p {
    font-size: var(--font-size-base);
  }
  
  .success-heart {
    font-size: 1.2rem;
  }
  
  .success-sparkle {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .success-content {
    padding: var(--space-4);
  }
  
  .success-circle {
    width: 80px;
    height: 80px;
  }
  
  .success-circle svg {
    width: 40px;
    height: 40px;
  }
  
  .success-message h2 {
    font-size: 1.25rem;
  }
  
  .success-heart {
    font-size: 1rem;
  }
}

/* Performance optimizations */
.success-animation * {
  will-change: transform, opacity;
}

@media (prefers-reduced-motion: reduce) {
  .success-circle,
  .success-heart,
  .success-sparkle,
  .success-confetti-piece,
  .loading-dots span {
    animation: none !important;
  }
  
  .success-icon,
  .success-message,
  .success-hearts,
  .success-sparkles,
  .success-confetti,
  .success-loading {
    transition: none !important;
    opacity: 1 !important;
    transform: none !important;
  }
}
