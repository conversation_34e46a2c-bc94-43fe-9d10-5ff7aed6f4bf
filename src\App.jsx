import { useState, useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import StarryBackground from './components/StarryBackground/StarryBackground';
import LandingPage from './components/LandingPage/LandingPage';
import RomanticDisplay from './components/RomanticDisplay/RomanticDisplay';
import Navigation from './components/Navigation/Navigation';
import Footer from './components/Footer/Footer';
import LoadingScreen from './components/LoadingScreen/LoadingScreen';
import './styles/animations.css';
import './App.css';

function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [isAppReady, setIsAppReady] = useState(false);

  useEffect(() => {
    // Simulate app initialization
    const initializeApp = async () => {
      // Add any initialization logic here
      await new Promise(resolve => setTimeout(resolve, 100));
      setIsAppReady(true);
    };

    initializeApp();
  }, []);

  const handleLoadingComplete = () => {
    setIsLoading(false);
  };

  if (!isAppReady) {
    return <div className="app-initializing">Initializing...</div>;
  }

  return (
    <div className="app">
      {isLoading && <LoadingScreen onComplete={handleLoadingComplete} />}

      <StarryBackground />
      <Navigation />

      <main className="main-content">
        <Routes>
          <Route path="/" element={<LandingPage />} />
          <Route path="/romantic/:id" element={<RomanticDisplay />} />
          <Route path="/gallery" element={<div className="page-placeholder">Gallery Coming Soon 💖</div>} />
          <Route path="/about" element={<div className="page-placeholder">About Us Coming Soon 💕</div>} />
        </Routes>
      </main>

      <Footer />
    </div>
  );
}

export default App;
