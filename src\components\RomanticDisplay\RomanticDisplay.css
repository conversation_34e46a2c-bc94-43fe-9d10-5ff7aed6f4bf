.romantic-display {
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  padding: var(--space-8) 0;
}

.romantic-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

/* Loading State */
.romantic-display.loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  text-align: center;
  color: var(--galaxy-white);
}

.loading-hearts {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.loading-hearts .heart {
  font-size: 2rem;
  color: var(--galaxy-pink);
}

/* Error State */
.romantic-display.error {
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-content {
  text-align: center;
  color: var(--galaxy-white);
  max-width: 500px;
}

.error-content h2 {
  margin-bottom: var(--space-4);
  color: var(--galaxy-gold);
}

.error-content p {
  margin-bottom: var(--space-6);
  color: var(--galaxy-silver);
}

.back-button {
  padding: var(--space-3) var(--space-6);
  background: linear-gradient(135deg, var(--galaxy-purple), var(--galaxy-light-purple));
  border: 2px solid var(--galaxy-gold);
  border-radius: var(--radius-lg);
  color: var(--galaxy-white);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.back-button:hover {
  background: linear-gradient(135deg, var(--galaxy-light-purple), var(--galaxy-purple));
  box-shadow: var(--shadow-glow);
  transform: translateY(-2px);
}

/* Names Section */
.names-section {
  text-align: center;
  margin-bottom: var(--space-16);
  padding: var(--space-8) 0;
}

.names-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-6);
}

.name {
  font-size: clamp(2.5rem, 8vw, 5rem);
  font-weight: 700;
  background: linear-gradient(135deg, var(--galaxy-gold), var(--galaxy-white), var(--galaxy-pink));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 40px rgba(255, 215, 0, 0.3);
  margin: 0;
  opacity: 0;
  transform: translateY(-100px) scale(0.8);
}

.name.animate-fall-from-top {
  animation: fallFromTop 1s ease-out forwards;
}

.heart-divider {
  font-size: 3rem;
  opacity: 0;
  transform: scale(0);
  filter: drop-shadow(0 0 20px var(--galaxy-pink));
}

.heart-divider.animate-heart-beat {
  animation: heartBeat 1.5s ease-in-out infinite;
  opacity: 1;
  transform: scale(1);
}

/* Message Section */
.message-section {
  margin-bottom: var(--space-16);
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s ease-out;
}

.message-section.visible {
  opacity: 1;
  transform: translateY(0);
}

.message-container {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.message-wrapper {
  background: rgba(26, 26, 58, 0.4);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  position: relative;
  overflow: hidden;
}

.message-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255, 215, 0, 0.1) 0%, 
    transparent 50%, 
    rgba(255, 105, 180, 0.1) 100%);
  pointer-events: none;
}

.love-message {
  font-size: clamp(1.1rem, 3vw, 1.5rem);
  line-height: 1.8;
  color: var(--galaxy-white);
  margin: 0;
  position: relative;
  z-index: 1;
  font-weight: 400;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.typewriter-text {
  border-right: 2px solid var(--galaxy-gold);
  animation: blinkCursor 1s step-end infinite;
}

.message-decoration {
  margin-top: var(--space-6);
  display: flex;
  justify-content: center;
  gap: var(--space-4);
}

.message-decoration .star {
  font-size: 1.5rem;
  animation: twinkle 2s ease-in-out infinite;
}

.message-decoration .star:nth-child(2) {
  animation-delay: 0.5s;
}

.message-decoration .star:nth-child(3) {
  animation-delay: 1s;
}

/* Gallery Section */
.gallery-section {
  margin-bottom: var(--space-16);
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s ease-out;
}

.gallery-section.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Actions Section */
.actions-section {
  text-align: center;
  margin-bottom: var(--space-8);
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s ease-out;
}

.actions-section.visible {
  opacity: 1;
  transform: translateY(0);
}

.actions-container {
  display: flex;
  justify-content: center;
  gap: var(--space-6);
  flex-wrap: wrap;
}

.action-button {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4) var(--space-6);
  background: linear-gradient(135deg, var(--galaxy-purple), var(--galaxy-light-purple));
  border: 2px solid var(--galaxy-gold);
  border-radius: var(--radius-lg);
  color: var(--galaxy-white);
  font-weight: 600;
  font-size: var(--font-size-base);
  cursor: pointer;
  transition: all var(--transition-normal);
  text-decoration: none;
  opacity: 0;
  transform: translateY(30px);
}

.action-button.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.action-button:hover {
  background: linear-gradient(135deg, var(--galaxy-light-purple), var(--galaxy-purple));
  box-shadow: var(--shadow-glow);
  transform: translateY(-2px);
}

.button-icon {
  width: 20px;
  height: 20px;
  stroke-width: 2;
}

.share-button {
  background: linear-gradient(135deg, var(--galaxy-pink), #ff1493);
}

.share-button:hover {
  background: linear-gradient(135deg, #ff1493, var(--galaxy-pink));
}

/* Floating Hearts */
.floating-hearts {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.floating-heart {
  position: absolute;
  font-size: 1.5rem;
  opacity: 0;
  pointer-events: none;
}

.floating-heart.animate {
  animation: floatUp 6s ease-out infinite;
}

@keyframes floatUp {
  0% {
    opacity: 0;
    transform: translateY(100vh) scale(0) rotate(0deg);
  }
  10% {
    opacity: 1;
    transform: translateY(90vh) scale(1) rotate(10deg);
  }
  90% {
    opacity: 1;
    transform: translateY(10vh) scale(1.2) rotate(-10deg);
  }
  100% {
    opacity: 0;
    transform: translateY(-10vh) scale(0) rotate(20deg);
  }
}

/* Enhanced Floating Elements */
.floating-emojis {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.floating-emoji {
  position: absolute;
  font-size: 1.2rem;
  opacity: 0;
  pointer-events: none;
}

.floating-emoji.animate {
  animation: floatUpEmoji 8s ease-out infinite;
}

@keyframes floatUpEmoji {
  0% {
    opacity: 0;
    transform: translateY(100vh) scale(0) rotate(0deg);
  }
  15% {
    opacity: 0.8;
    transform: translateY(85vh) scale(1) rotate(15deg);
  }
  85% {
    opacity: 0.8;
    transform: translateY(15vh) scale(1.1) rotate(-15deg);
  }
  100% {
    opacity: 0;
    transform: translateY(-10vh) scale(0) rotate(30deg);
  }
}

/* Celebration Confetti */
.celebration-confetti {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
}

.confetti-piece {
  position: absolute;
  width: 8px;
  height: 8px;
  animation: confettiFall linear;
}

@keyframes confettiFall {
  0% {
    opacity: 1;
    transform: translateY(-100vh) rotate(0deg);
  }
  100% {
    opacity: 0;
    transform: translateY(100vh) rotate(720deg);
  }
}

/* Sparkle Effects */
.sparkle-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.sparkle {
  position: absolute;
  font-size: 0.8rem;
  opacity: 0;
  pointer-events: none;
}

.sparkle.animate {
  animation: sparkleShine 4s ease-in-out infinite;
}

@keyframes sparkleShine {
  0%, 100% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.5) rotate(180deg);
  }
}

/* Cascading Animation System */
.cascade-hidden {
  opacity: 0;
  visibility: hidden;
  transform: translateY(-100px) scale(0.8) rotateX(15deg);
  will-change: transform, opacity;
}

.cascade-fall-in {
  opacity: 1;
  visibility: visible;
  transform: translateY(0) scale(1) rotateX(0deg);
  animation: cascadeFallIn 1.2s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
  will-change: transform, opacity;
}

.cascade-fade-in {
  opacity: 1;
  visibility: visible;
  transform: translateY(0) scale(1);
  animation: cascadeFadeIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  will-change: transform, opacity;
}

.cascade-visible {
  opacity: 1;
  visibility: visible;
}

/* Cascade Delay Classes */
.cascade-delay-1 { animation-delay: 0.3s; }
.cascade-delay-2 { animation-delay: 0.5s; }
.cascade-delay-3 { animation-delay: 0.7s; }
.cascade-delay-4 { animation-delay: 0.9s; }
.cascade-delay-5 { animation-delay: 1.1s; }

/* Professional Cascading Keyframes */
@keyframes cascadeFallIn {
  0% {
    opacity: 0;
    transform: translateY(-120px) scale(0.7) rotateX(20deg) rotateZ(-5deg);
    filter: blur(3px);
  }
  60% {
    opacity: 0.8;
    transform: translateY(10px) scale(1.05) rotateX(-2deg) rotateZ(1deg);
    filter: blur(0px);
  }
  80% {
    transform: translateY(-5px) scale(0.98) rotateX(1deg) rotateZ(-0.5deg);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1) rotateX(0deg) rotateZ(0deg);
    filter: blur(0px);
  }
}

@keyframes cascadeFadeIn {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
    filter: blur(2px);
  }
  70% {
    transform: translateY(-3px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0px);
  }
}

/* Gallery Title Animation */
.gallery-title {
  color: var(--galaxy-gold);
  font-size: var(--font-size-xl);
  font-weight: 600;
  text-align: center;
  margin-bottom: var(--space-6);
  opacity: 0;
  transform: translateY(20px);
  animation: cascadeFadeIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s forwards;
}

/* Love Rain Effect - Hiệu ứng mưa tình yêu */
.love-rain-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: 5;
  overflow: hidden;
}

.rain-element {
  position: absolute;
  pointer-events: none;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  will-change: transform, opacity;
  user-select: none;
  transition: none;
}

/* Different types of rain elements */
.rain-name1,
.rain-name2 {
  font-family: 'Dancing Script', cursive;
  font-weight: 700;
  text-shadow:
    0 0 10px currentColor,
    0 0 20px rgba(255, 255, 255, 0.5),
    2px 2px 4px rgba(0, 0, 0, 0.3);
  filter: drop-shadow(0 0 8px currentColor);
}

.rain-heart {
  font-size: 1.5em;
  filter: drop-shadow(0 0 10px #FF1493);
  animation: heartPulse 2s ease-in-out infinite;
}

.rain-emoji {
  font-size: 1.2em;
  filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.8));
}

.rain-text {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow:
    0 0 8px currentColor,
    2px 2px 4px rgba(0, 0, 0, 0.5);
}

.rain-image {
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow:
    0 0 15px rgba(255, 255, 255, 0.6),
    0 4px 8px rgba(0, 0, 0, 0.3);
  object-fit: cover;
  filter: brightness(1.1) contrast(1.1);
}

/* Heart pulse animation for rain hearts */
@keyframes heartPulse {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 10px #FF1493);
  }
  50% {
    transform: scale(1.2);
    filter: drop-shadow(0 0 20px #FF1493);
  }
}

/* Rain Content Styles */
.rain-content-section {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: var(--space-8);
  position: relative;
  z-index: 10;
}

.rain-content-container {
  text-align: center;
  background: rgba(26, 26, 58, 0.8);
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 215, 0, 0.3);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-xl);
  max-width: 600px;
  width: 100%;
}

.rain-message-display {
  margin-bottom: var(--space-8);
}

.rain-title {
  font-family: 'Dancing Script', cursive;
  font-size: 3rem;
  font-weight: 700;
  color: var(--galaxy-gold);
  margin-bottom: var(--space-4);
  text-shadow:
    0 0 20px var(--galaxy-gold),
    0 0 40px rgba(255, 215, 0, 0.5);
  animation: titleGlow 3s ease-in-out infinite alternate;
}

.rain-love-message {
  font-size: var(--font-size-lg);
  color: var(--galaxy-white);
  line-height: 1.8;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.rain-actions {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
  flex-wrap: wrap;
}

.rain-action-button {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  background: linear-gradient(135deg, var(--galaxy-purple), var(--galaxy-pink));
  border: 2px solid var(--galaxy-gold);
  border-radius: var(--radius-full);
  color: var(--galaxy-white);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  text-decoration: none;
  font-size: var(--font-size-base);
}

.rain-action-button:hover {
  background: linear-gradient(135deg, var(--galaxy-pink), var(--galaxy-purple));
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow);
}

.rain-action-button .button-icon {
  width: 20px;
  height: 20px;
  stroke-width: 2;
}

@keyframes titleGlow {
  0% {
    text-shadow:
      0 0 20px var(--galaxy-gold),
      0 0 40px rgba(255, 215, 0, 0.5);
  }
  100% {
    text-shadow:
      0 0 30px var(--galaxy-gold),
      0 0 60px rgba(255, 215, 0, 0.8);
  }
}

/* Responsive adjustments for rain */
@media (max-width: 768px) {
  .rain-element {
    font-size: 0.9em;
  }

  .rain-image {
    max-width: 40px !important;
    max-height: 40px !important;
  }

  .rain-content-container {
    padding: var(--space-6);
    margin: var(--space-4);
  }

  .rain-title {
    font-size: 2.5rem;
  }

  .rain-actions {
    flex-direction: column;
    align-items: center;
  }

  .rain-action-button {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .rain-element {
    font-size: 0.8em;
  }

  .rain-image {
    max-width: 30px !important;
    max-height: 30px !important;
  }

  .rain-content-container {
    padding: var(--space-4);
    margin: var(--space-2);
  }

  .rain-title {
    font-size: 2rem;
  }

  .rain-love-message {
    font-size: var(--font-size-base);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .romantic-display {
    padding: var(--space-4) 0;
  }
  
  .romantic-content {
    padding: 0 var(--space-2);
  }
  
  .names-section {
    margin-bottom: var(--space-12);
    padding: var(--space-4) 0;
  }
  
  .names-container {
    gap: var(--space-4);
  }
  
  .heart-divider {
    font-size: 2rem;
  }
  
  .message-section,
  .gallery-section,
  .actions-section {
    margin-bottom: var(--space-12);
  }
  
  .message-wrapper {
    padding: var(--space-6);
  }
  
  .actions-container {
    flex-direction: column;
    align-items: center;
    gap: var(--space-4);
  }
  
  .action-button {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
  
  .floating-heart {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .names-container {
    gap: var(--space-3);
  }
  
  .heart-divider {
    font-size: 1.5rem;
  }
  
  .message-wrapper {
    padding: var(--space-4);
  }
  
  .love-message {
    font-size: 1rem;
    line-height: 1.6;
  }
  
  .floating-heart {
    font-size: 1rem;
  }
}

/* Animation Performance */
.romantic-display * {
  will-change: transform, opacity;
}

@media (prefers-reduced-motion: reduce) {
  .name,
  .heart-divider,
  .message-section,
  .gallery-section,
  .actions-section,
  .floating-heart,
  .action-button,
  .cascade-fall-in,
  .cascade-fade-in,
  .cascade-hidden,
  .rain-element,
  .love-rain-container {
    animation: none !important;
    transition: none !important;
    opacity: 1 !important;
    visibility: visible !important;
    transform: none !important;
  }

  /* Hide rain effect for users who prefer reduced motion */
  .love-rain-container {
    display: none !important;
  }
}
