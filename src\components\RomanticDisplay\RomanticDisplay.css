.romantic-display {
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  padding: var(--space-8) 0;
}

.romantic-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

/* Loading State */
.romantic-display.loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  text-align: center;
  color: var(--galaxy-white);
}

.loading-hearts {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.loading-hearts .heart {
  font-size: 2rem;
  color: var(--galaxy-pink);
}

/* Error State */
.romantic-display.error {
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-content {
  text-align: center;
  color: var(--galaxy-white);
  max-width: 500px;
}

.error-content h2 {
  margin-bottom: var(--space-4);
  color: var(--galaxy-gold);
}

.error-content p {
  margin-bottom: var(--space-6);
  color: var(--galaxy-silver);
}

.back-button {
  padding: var(--space-3) var(--space-6);
  background: linear-gradient(135deg, var(--galaxy-purple), var(--galaxy-light-purple));
  border: 2px solid var(--galaxy-gold);
  border-radius: var(--radius-lg);
  color: var(--galaxy-white);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.back-button:hover {
  background: linear-gradient(135deg, var(--galaxy-light-purple), var(--galaxy-purple));
  box-shadow: var(--shadow-glow);
  transform: translateY(-2px);
}

/* Names Section */
.names-section {
  text-align: center;
  margin-bottom: var(--space-16);
  padding: var(--space-8) 0;
}

.names-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-6);
}

.name {
  font-size: clamp(2.5rem, 8vw, 5rem);
  font-weight: 700;
  background: linear-gradient(135deg, var(--galaxy-gold), var(--galaxy-white), var(--galaxy-pink));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 40px rgba(255, 215, 0, 0.3);
  margin: 0;
  opacity: 0;
  transform: translateY(-100px) scale(0.8);
}

.name.animate-fall-from-top {
  animation: fallFromTop 1s ease-out forwards;
}

.heart-divider {
  font-size: 3rem;
  opacity: 0;
  transform: scale(0);
  filter: drop-shadow(0 0 20px var(--galaxy-pink));
}

.heart-divider.animate-heart-beat {
  animation: heartBeat 1.5s ease-in-out infinite;
  opacity: 1;
  transform: scale(1);
}

/* Message Section */
.message-section {
  margin-bottom: var(--space-16);
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s ease-out;
}

.message-section.visible {
  opacity: 1;
  transform: translateY(0);
}

.message-container {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.message-wrapper {
  background: rgba(26, 26, 58, 0.4);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  position: relative;
  overflow: hidden;
}

.message-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255, 215, 0, 0.1) 0%, 
    transparent 50%, 
    rgba(255, 105, 180, 0.1) 100%);
  pointer-events: none;
}

.love-message {
  font-size: clamp(1.1rem, 3vw, 1.5rem);
  line-height: 1.8;
  color: var(--galaxy-white);
  margin: 0;
  position: relative;
  z-index: 1;
  font-weight: 400;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.typewriter-text {
  border-right: 2px solid var(--galaxy-gold);
  animation: blinkCursor 1s step-end infinite;
}

.message-decoration {
  margin-top: var(--space-6);
  display: flex;
  justify-content: center;
  gap: var(--space-4);
}

.message-decoration .star {
  font-size: 1.5rem;
  animation: twinkle 2s ease-in-out infinite;
}

.message-decoration .star:nth-child(2) {
  animation-delay: 0.5s;
}

.message-decoration .star:nth-child(3) {
  animation-delay: 1s;
}

/* Gallery Section */
.gallery-section {
  margin-bottom: var(--space-16);
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s ease-out;
}

.gallery-section.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Actions Section */
.actions-section {
  text-align: center;
  margin-bottom: var(--space-8);
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s ease-out;
}

.actions-section.visible {
  opacity: 1;
  transform: translateY(0);
}

.actions-container {
  display: flex;
  justify-content: center;
  gap: var(--space-6);
  flex-wrap: wrap;
}

.action-button {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4) var(--space-6);
  background: linear-gradient(135deg, var(--galaxy-purple), var(--galaxy-light-purple));
  border: 2px solid var(--galaxy-gold);
  border-radius: var(--radius-lg);
  color: var(--galaxy-white);
  font-weight: 600;
  font-size: var(--font-size-base);
  cursor: pointer;
  transition: all var(--transition-normal);
  text-decoration: none;
  opacity: 0;
  transform: translateY(30px);
}

.action-button.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.action-button:hover {
  background: linear-gradient(135deg, var(--galaxy-light-purple), var(--galaxy-purple));
  box-shadow: var(--shadow-glow);
  transform: translateY(-2px);
}

.button-icon {
  width: 20px;
  height: 20px;
  stroke-width: 2;
}

.share-button {
  background: linear-gradient(135deg, var(--galaxy-pink), #ff1493);
}

.share-button:hover {
  background: linear-gradient(135deg, #ff1493, var(--galaxy-pink));
}

/* Floating Hearts */
.floating-hearts {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.floating-heart {
  position: absolute;
  font-size: 1.5rem;
  opacity: 0;
  pointer-events: none;
}

.floating-heart.animate {
  animation: floatUp 6s ease-out infinite;
}

@keyframes floatUp {
  0% {
    opacity: 0;
    transform: translateY(100vh) scale(0) rotate(0deg);
  }
  10% {
    opacity: 1;
    transform: translateY(90vh) scale(1) rotate(10deg);
  }
  90% {
    opacity: 1;
    transform: translateY(10vh) scale(1.2) rotate(-10deg);
  }
  100% {
    opacity: 0;
    transform: translateY(-10vh) scale(0) rotate(20deg);
  }
}

/* Enhanced Floating Elements */
.floating-emojis {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.floating-emoji {
  position: absolute;
  font-size: 1.2rem;
  opacity: 0;
  pointer-events: none;
}

.floating-emoji.animate {
  animation: floatUpEmoji 8s ease-out infinite;
}

@keyframes floatUpEmoji {
  0% {
    opacity: 0;
    transform: translateY(100vh) scale(0) rotate(0deg);
  }
  15% {
    opacity: 0.8;
    transform: translateY(85vh) scale(1) rotate(15deg);
  }
  85% {
    opacity: 0.8;
    transform: translateY(15vh) scale(1.1) rotate(-15deg);
  }
  100% {
    opacity: 0;
    transform: translateY(-10vh) scale(0) rotate(30deg);
  }
}

/* Celebration Confetti */
.celebration-confetti {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
}

.confetti-piece {
  position: absolute;
  width: 8px;
  height: 8px;
  animation: confettiFall linear;
}

@keyframes confettiFall {
  0% {
    opacity: 1;
    transform: translateY(-100vh) rotate(0deg);
  }
  100% {
    opacity: 0;
    transform: translateY(100vh) rotate(720deg);
  }
}

/* Sparkle Effects */
.sparkle-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.sparkle {
  position: absolute;
  font-size: 0.8rem;
  opacity: 0;
  pointer-events: none;
}

.sparkle.animate {
  animation: sparkleShine 4s ease-in-out infinite;
}

@keyframes sparkleShine {
  0%, 100% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.5) rotate(180deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .romantic-display {
    padding: var(--space-4) 0;
  }
  
  .romantic-content {
    padding: 0 var(--space-2);
  }
  
  .names-section {
    margin-bottom: var(--space-12);
    padding: var(--space-4) 0;
  }
  
  .names-container {
    gap: var(--space-4);
  }
  
  .heart-divider {
    font-size: 2rem;
  }
  
  .message-section,
  .gallery-section,
  .actions-section {
    margin-bottom: var(--space-12);
  }
  
  .message-wrapper {
    padding: var(--space-6);
  }
  
  .actions-container {
    flex-direction: column;
    align-items: center;
    gap: var(--space-4);
  }
  
  .action-button {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
  
  .floating-heart {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .names-container {
    gap: var(--space-3);
  }
  
  .heart-divider {
    font-size: 1.5rem;
  }
  
  .message-wrapper {
    padding: var(--space-4);
  }
  
  .love-message {
    font-size: 1rem;
    line-height: 1.6;
  }
  
  .floating-heart {
    font-size: 1rem;
  }
}

/* Animation Performance */
.romantic-display * {
  will-change: transform, opacity;
}

@media (prefers-reduced-motion: reduce) {
  .name,
  .heart-divider,
  .message-section,
  .gallery-section,
  .actions-section,
  .floating-heart,
  .action-button {
    animation: none !important;
    transition: none !important;
    opacity: 1 !important;
    transform: none !important;
  }
}
