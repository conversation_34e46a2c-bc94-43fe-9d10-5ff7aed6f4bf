import React, { useState } from 'react';
import './ThemeSelector.css';

const themes = [
  {
    id: 'galaxy',
    name: 'Galaxy Romance',
    description: 'Deep space blues with golden stars',
    colors: {
      primary: '#0a0a1a',
      secondary: '#1a1a3a',
      accent: '#ffd700',
      text: '#ffffff'
    },
    preview: '🌌'
  },
  {
    id: 'sunset',
    name: 'Sunset Dreams',
    description: 'Warm oranges and romantic pinks',
    colors: {
      primary: '#1a0a0a',
      secondary: '#3a1a1a',
      accent: '#ff6b35',
      text: '#ffffff'
    },
    preview: '🌅'
  },
  {
    id: 'ocean',
    name: 'Ocean Breeze',
    description: 'Calming blues and seafoam greens',
    colors: {
      primary: '#0a1a1a',
      secondary: '#1a3a3a',
      accent: '#20b2aa',
      text: '#ffffff'
    },
    preview: '🌊'
  },
  {
    id: 'forest',
    name: 'Enchanted Forest',
    description: 'Deep greens with golden highlights',
    colors: {
      primary: '#0a1a0a',
      secondary: '#1a3a1a',
      accent: '#32cd32',
      text: '#ffffff'
    },
    preview: '🌲'
  },
  {
    id: 'cherry',
    name: 'Cherry Blossom',
    description: 'Soft pinks and delicate whites',
    colors: {
      primary: '#1a0a1a',
      secondary: '#3a1a3a',
      accent: '#ffb6c1',
      text: '#ffffff'
    },
    preview: '🌸'
  }
];

const ThemeSelector = ({ currentTheme, onThemeChange, isOpen, onToggle }) => {
  const [hoveredTheme, setHoveredTheme] = useState(null);

  const handleThemeSelect = (theme) => {
    onThemeChange(theme);
    // Apply theme to CSS variables
    const root = document.documentElement;
    root.style.setProperty('--galaxy-deep-blue', theme.colors.primary);
    root.style.setProperty('--galaxy-dark-blue', theme.colors.secondary);
    root.style.setProperty('--galaxy-gold', theme.colors.accent);
    root.style.setProperty('--galaxy-white', theme.colors.text);
  };

  const selectedTheme = themes.find(theme => theme.id === currentTheme) || themes[0];

  return (
    <div className="theme-selector">
      {/* Theme Toggle Button */}
      <button
        className="theme-toggle"
        onClick={onToggle}
        aria-label="Toggle theme selector"
        title="Change theme"
      >
        <span className="theme-preview">{selectedTheme.preview}</span>
        <svg className="theme-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="12" cy="12" r="3" />
          <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m15.5-6.5l-4.24 4.24M7.76 16.24l-4.24 4.24M16.24 7.76l4.24-4.24M7.76 7.76L3.52 3.52" />
        </svg>
      </button>

      {/* Theme Selector Panel */}
      {isOpen && (
        <div className="theme-panel">
          <div className="theme-header">
            <h3>Choose Your Theme</h3>
            <p>Select the perfect atmosphere for your love story</p>
          </div>
          
          <div className="theme-grid">
            {themes.map((theme) => (
              <button
                key={theme.id}
                className={`theme-option ${currentTheme === theme.id ? 'active' : ''}`}
                onClick={() => handleThemeSelect(theme)}
                onMouseEnter={() => setHoveredTheme(theme.id)}
                onMouseLeave={() => setHoveredTheme(null)}
                style={{
                  '--theme-primary': theme.colors.primary,
                  '--theme-secondary': theme.colors.secondary,
                  '--theme-accent': theme.colors.accent,
                  '--theme-text': theme.colors.text
                }}
              >
                <div className="theme-preview-large">
                  <span className="theme-emoji">{theme.preview}</span>
                  <div className="theme-colors">
                    <div 
                      className="color-dot" 
                      style={{ backgroundColor: theme.colors.primary }}
                    />
                    <div 
                      className="color-dot" 
                      style={{ backgroundColor: theme.colors.secondary }}
                    />
                    <div 
                      className="color-dot" 
                      style={{ backgroundColor: theme.colors.accent }}
                    />
                  </div>
                </div>
                
                <div className="theme-info">
                  <h4>{theme.name}</h4>
                  <p>{theme.description}</p>
                </div>
                
                {currentTheme === theme.id && (
                  <div className="theme-selected">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <polyline points="20,6 9,17 4,12" />
                    </svg>
                  </div>
                )}
                
                {hoveredTheme === theme.id && (
                  <div className="theme-hover-effect" />
                )}
              </button>
            ))}
          </div>
          
          <div className="theme-footer">
            <p>✨ Themes enhance the visual experience of your love story</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default ThemeSelector;
