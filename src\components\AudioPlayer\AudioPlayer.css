.audio-player {
  position: fixed;
  bottom: var(--space-6);
  right: var(--space-6);
  z-index: 100;
  transition: all var(--transition-normal);
}

.audio-player.error {
  position: relative;
  bottom: auto;
  right: auto;
  margin: var(--space-4) 0;
}

.error-message {
  background: rgba(255, 68, 68, 0.1);
  border: 1px solid rgba(255, 68, 68, 0.3);
  border-radius: var(--radius-md);
  padding: var(--space-3);
  color: #ff4444;
  font-size: var(--font-size-sm);
  text-align: center;
}

/* Floating Music Button */
.floating-music-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--galaxy-purple), var(--galaxy-light-purple));
  border: 2px solid var(--galaxy-gold);
  color: var(--galaxy-white);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all var(--transition-normal);
  box-shadow:
    var(--shadow-lg),
    0 0 20px rgba(255, 215, 0, 0.3);
  animation: pulseGlow 2s ease-in-out infinite;
  overflow: hidden;
}

.floating-music-button::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.1), transparent);
  animation: musicButtonShine 3s linear infinite;
}

@keyframes musicButtonShine {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.floating-music-button:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-glow);
}

.music-icon {
  width: 24px;
  height: 24px;
  stroke-width: 2;
  z-index: 1;
}

.music-waves {
  position: absolute;
  right: -8px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.music-waves span {
  width: 3px;
  background: var(--galaxy-gold);
  border-radius: 2px;
  animation: musicWave 1.5s ease-in-out infinite;
}

.music-waves span:nth-child(1) {
  height: 8px;
  animation-delay: 0s;
}

.music-waves span:nth-child(2) {
  height: 12px;
  animation-delay: 0.2s;
}

.music-waves span:nth-child(3) {
  height: 6px;
  animation-delay: 0.4s;
}

@keyframes musicWave {
  0%, 100% {
    transform: scaleY(0.5);
    opacity: 0.7;
  }
  50% {
    transform: scaleY(1);
    opacity: 1;
  }
}

/* Full Audio Controls */
.audio-controls {
  background: rgba(26, 26, 58, 0.95);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: var(--radius-xl);
  padding: var(--space-4);
  min-width: 350px;
  box-shadow: var(--shadow-xl);
  animation: slideInFromBottom 0.3s ease-out;
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.controls-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-4);
}

.track-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
  flex: 1;
}

.track-name {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--galaxy-white);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 250px;
}

.track-status {
  font-size: var(--font-size-xs);
  color: var(--galaxy-silver);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.minimize-button {
  background: none;
  border: 1px solid var(--galaxy-dark-blue);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  color: var(--galaxy-silver);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.minimize-button:hover {
  border-color: var(--galaxy-gold);
  color: var(--galaxy-gold);
}

.minimize-button svg {
  width: 12px;
  height: 12px;
  stroke-width: 2;
}

.main-controls {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.play-pause-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--galaxy-gold), #ffed4e);
  border: none;
  color: var(--galaxy-deep-blue);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.play-pause-button:hover:not(:disabled) {
  transform: scale(1.1);
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.play-pause-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.play-pause-button svg {
  width: 18px;
  height: 18px;
  stroke-width: 2;
  fill: currentColor;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid var(--galaxy-deep-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex: 1;
}

.time-display {
  font-size: var(--font-size-xs);
  color: var(--galaxy-silver);
  font-weight: 500;
  min-width: 35px;
  text-align: center;
}

.progress-bar {
  flex: 1;
  height: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: var(--space-2) 0;
}

.progress-track {
  width: 100%;
  height: 4px;
  background: var(--galaxy-dark-blue);
  border-radius: 2px;
  position: relative;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--galaxy-gold), var(--galaxy-pink));
  border-radius: 2px;
  transition: width 0.1s ease;
}

.progress-thumb {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 12px;
  height: 12px;
  background: var(--galaxy-white);
  border: 2px solid var(--galaxy-gold);
  border-radius: 50%;
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.progress-bar:hover .progress-thumb {
  opacity: 1;
}

.volume-controls {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.volume-button {
  background: none;
  border: none;
  color: var(--galaxy-silver);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color var(--transition-fast);
  padding: var(--space-1);
}

.volume-button:hover {
  color: var(--galaxy-gold);
}

.volume-button svg {
  width: 16px;
  height: 16px;
  stroke-width: 2;
}

.volume-slider {
  width: 60px;
  height: 4px;
  background: var(--galaxy-dark-blue);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  background: var(--galaxy-gold);
  border-radius: 50%;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.volume-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
}

.volume-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  background: var(--galaxy-gold);
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.volume-slider::-moz-range-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
  .audio-player {
    bottom: var(--space-4);
    right: var(--space-4);
  }
  
  .floating-music-button {
    width: 50px;
    height: 50px;
  }
  
  .music-icon {
    width: 20px;
    height: 20px;
  }
  
  .audio-controls {
    min-width: 300px;
    padding: var(--space-3);
  }
  
  .main-controls {
    gap: var(--space-3);
  }
  
  .volume-controls {
    display: none; /* Hide volume controls on mobile */
  }
  
  .progress-container {
    gap: var(--space-2);
  }
}

@media (max-width: 480px) {
  .audio-controls {
    min-width: 280px;
    right: var(--space-2);
    left: var(--space-2);
    width: auto;
  }
  
  .track-name {
    max-width: 180px;
  }
  
  .time-display {
    min-width: 30px;
    font-size: 10px;
  }
}

/* Accessibility */
.play-pause-button:focus,
.volume-button:focus,
.minimize-button:focus,
.floating-music-button:focus {
  outline: 2px solid var(--galaxy-gold);
  outline-offset: 2px;
}

.progress-bar:focus {
  outline: 2px solid var(--galaxy-gold);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Performance optimizations */
.floating-music-button,
.audio-controls {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

@media (prefers-reduced-motion: reduce) {
  .floating-music-button,
  .music-waves span,
  .audio-controls,
  .loading-spinner {
    animation: none !important;
  }
  
  .floating-music-button:hover,
  .play-pause-button:hover {
    transform: none !important;
  }
}
