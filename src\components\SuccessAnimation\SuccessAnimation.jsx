import React, { useEffect, useState } from 'react';
import './SuccessAnimation.css';

const SuccessAnimation = ({ onComplete }) => {
  const [stage, setStage] = useState(0);

  useEffect(() => {
    const timer1 = setTimeout(() => setStage(1), 500);
    const timer2 = setTimeout(() => setStage(2), 1500);
    const timer3 = setTimeout(() => setStage(3), 2500);
    const timer4 = setTimeout(() => {
      setStage(4);
      if (onComplete) {
        setTimeout(onComplete, 1000);
      }
    }, 3500);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
      clearTimeout(timer4);
    };
  }, [onComplete]);

  return (
    <div className="success-animation">
      <div className="success-content">
        {/* Success Icon */}
        <div className={`success-icon ${stage >= 1 ? 'animate' : ''}`}>
          <div className="success-circle">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
              <polyline points="22,4 12,14.01 9,11.01" />
            </svg>
          </div>
        </div>

        {/* Success Message */}
        <div className={`success-message ${stage >= 2 ? 'animate' : ''}`}>
          <h2>Love Story Created! 💖</h2>
          <p>Your beautiful romantic experience is ready to be shared</p>
        </div>

        {/* Floating Hearts */}
        <div className={`success-hearts ${stage >= 3 ? 'animate' : ''}`}>
          {[...Array(12)].map((_, index) => (
            <div 
              key={index}
              className="success-heart"
              style={{
                left: `${10 + (index * 7)}%`,
                animationDelay: `${index * 0.2}s`,
                animationDuration: `${3 + (index % 3)}s`
              }}
            >
              {index % 4 === 0 ? '💖' : 
               index % 4 === 1 ? '💕' : 
               index % 4 === 2 ? '💗' : '💝'}
            </div>
          ))}
        </div>

        {/* Sparkle Effects */}
        <div className={`success-sparkles ${stage >= 1 ? 'animate' : ''}`}>
          {[...Array(20)].map((_, index) => (
            <div 
              key={index}
              className="success-sparkle"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 2}s`,
                animationDuration: `${1.5 + Math.random()}s`
              }}
            >
              ✨
            </div>
          ))}
        </div>

        {/* Confetti */}
        <div className={`success-confetti ${stage >= 2 ? 'animate' : ''}`}>
          {[...Array(30)].map((_, index) => (
            <div 
              key={index}
              className="success-confetti-piece"
              style={{
                left: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 1}s`,
                animationDuration: `${2 + Math.random()}s`,
                backgroundColor: index % 5 === 0 ? '#ffd700' : 
                                 index % 5 === 1 ? '#ff69b4' : 
                                 index % 5 === 2 ? '#4c2a85' : 
                                 index % 5 === 3 ? '#ffffff' : '#ff1493'
              }}
            />
          ))}
        </div>

        {/* Loading Dots */}
        <div className={`success-loading ${stage >= 4 ? 'animate' : ''}`}>
          <p>Redirecting to your love story</p>
          <div className="loading-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuccessAnimation;
