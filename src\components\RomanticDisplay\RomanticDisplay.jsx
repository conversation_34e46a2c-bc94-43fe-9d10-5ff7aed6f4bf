import { useState, useEffect, useRef, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { getRomanticPage } from '../../utils/storage';
import { getRandomRomanticEmojis } from '../../utils/loveQuotes';
// import ImageGallery from '../ImageGallery/ImageGallery';
// import AudioPlayer from '../AudioPlayer/AudioPlayer';
import QRGenerator from '../QRGenerator/QRGenerator';
import './RomanticDisplay.css';

const RomanticDisplay = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [pageData, setPageData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showQR, setShowQR] = useState(false);
  const [animationStage, setAnimationStage] = useState(0);
  const [romanticEmojis, setRomanticEmojis] = useState([]);
  const [showCelebration, setShowCelebration] = useState(false);
  const [rainElements, setRainElements] = useState([]);
  const messageRef = useRef(null);
  const rainIntervalRef = useRef(null);

  // Tạo hiệu ứng mưa tình yêu
  const createRainElement = useCallback(() => {
    if (!pageData) return null;

    const rainTypes = [
      { type: 'name1', content: pageData.myName, color: '#FFD700' },
      { type: 'name2', content: pageData.partnerName, color: '#FF69B4' },
      { type: 'heart', content: '💖', color: '#FF1493' },
      { type: 'emoji', content: '💕', color: '#FF69B4' },
      { type: 'emoji', content: '💝', color: '#FFB6C1' },
      { type: 'emoji', content: '🌹', color: '#FF6347' },
      { type: 'emoji', content: '✨', color: '#FFD700' },
      { type: 'emoji', content: '💫', color: '#DDA0DD' },
      { type: 'text', content: 'Love', color: '#FF1493' },
      { type: 'text', content: 'Forever', color: '#FF69B4' },
      { type: 'text', content: 'Together', color: '#FFB6C1' },
    ];

    // Thêm hình ảnh vào mưa nếu có
    if (pageData.images && pageData.images.length > 0) {
      pageData.images.forEach((image, index) => {
        rainTypes.push({
          type: 'image',
          content: image.base64 || image.preview,
          color: '#FFFFFF',
          id: `img-${index}`
        });
      });
    }

    const randomType = rainTypes[Math.floor(Math.random() * rainTypes.length)];

    return {
      id: Date.now() + Math.random(),
      ...randomType,
      x: Math.random() * 100, // vị trí ngang ngẫu nhiên (%)
      y: -10, // bắt đầu từ trên màn hình
      speed: 0.5 + Math.random() * 1.5, // tốc độ rơi ngẫu nhiên
      rotation: Math.random() * 360, // góc xoay ngẫu nhiên
      scale: 0.8 + Math.random() * 0.4, // kích thước ngẫu nhiên
      opacity: 0.7 + Math.random() * 0.3, // độ trong suốt ngẫu nhiên
      sway: Math.random() * 2 - 1, // dao động ngang
    };
  }, [pageData]);

  useEffect(() => {
    const loadPageData = () => {
      const data = getRomanticPage(id);
      if (data) {
        setPageData(data);
        setRomanticEmojis(getRandomRomanticEmojis(12));

        // Bắt đầu hiệu ứng mưa tình yêu sau 2 giây
        setTimeout(() => {
          setAnimationStage(1);

          // Tạo mưa tình yêu liên tục
          rainIntervalRef.current = setInterval(() => {
            const newElement = createRainElement();
            if (newElement) {
              setRainElements(prev => {
                // Giữ tối đa 50 phần tử để tránh lag
                const filtered = prev.filter(el => el.y < 110);
                return [...filtered, newElement].slice(-50);
              });
            }
          }, 300 + Math.random() * 500); // Tạo phần tử mới mỗi 300-800ms

        }, 2000);

        // Celebration effects
        setTimeout(() => setShowCelebration(true), 8000);
        setTimeout(() => setShowCelebration(false), 12000);
      } else {
        // Page not found, redirect to home
        navigate('/');
      }
      setLoading(false);
    };

    loadPageData();

    // Cleanup khi component unmount
    return () => {
      if (rainIntervalRef.current) {
        clearInterval(rainIntervalRef.current);
      }
    };
  }, [id, navigate]);

  // Update rain elements animation
  useEffect(() => {
    if (rainElements.length === 0) return;

    const animationFrame = setInterval(() => {
      setRainElements(prev =>
        prev.map(element => ({
          ...element,
          y: element.y + element.speed,
          x: element.x + element.sway * 0.1,
          rotation: element.rotation + 1,
        })).filter(element => element.y < 110)
      );
    }, 16); // 60fps

    return () => clearInterval(animationFrame);
  }, [rainElements.length]);

  useEffect(() => {
    // Typewriter effect for the message
    if (animationStage >= 3 && pageData && messageRef.current) {
      const message = pageData.loveMessage;
      const element = messageRef.current;
      element.textContent = '';
      
      let i = 0;
      const typeWriter = () => {
        if (i < message.length) {
          element.textContent += message.charAt(i);
          i++;
          setTimeout(typeWriter, 50);
        }
      };
      
      setTimeout(typeWriter, 500);
    }
  }, [animationStage, pageData]);

  if (loading) {
    return (
      <div className="romantic-display loading">
        <div className="loading-content">
          <div className="loading-hearts">
            <div className="heart animate-heart-beat"></div>
            <div className="heart animate-heart-beat animate-delay-200"></div>
            <div className="heart animate-heart-beat animate-delay-400"></div>
          </div>
          <p>Loading your love story...</p>
        </div>
      </div>
    );
  }

  if (!pageData) {
    return (
      <div className="romantic-display error">
        <div className="error-content">
          <h2>Love Story Not Found</h2>
          <p>The romantic page you're looking for doesn't exist or has expired.</p>
          <button onClick={() => navigate('/')} className="back-button">
            Create New Love Story
          </button>
        </div>
      </div>
    );
  }

  const currentUrl = window.location.href;

  return (
    <div className="romantic-display">
      {/* Background Audio */}
      {pageData.audio && (
        <AudioPlayer 
          audioData={pageData.audio} 
          autoPlay={animationStage >= 4}
        />
      )}

      <div className="romantic-content">
        {/* Simple centered content for rain background */}
        <section className="rain-content-section">
          <div className="rain-content-container">
            <div className="rain-message-display">
              <h1 className="rain-title">
                {pageData.myName} 💖 {pageData.partnerName}
              </h1>
              <p className="rain-love-message">
                {pageData.loveMessage}
              </p>
            </div>

            {/* Action Buttons */}
            <div className="rain-actions">
              <button
                onClick={() => setShowQR(true)}
                className="rain-action-button share-button"
              >
                <svg className="button-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8" />
                  <polyline points="16,6 12,2 8,6" />
                  <line x1="12" y1="2" x2="12" y2="15" />
                </svg>
                Share Love Story
              </button>

              <button
                onClick={() => navigate('/')}
                className="rain-action-button create-button"
              >
                <svg className="button-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <circle cx="12" cy="12" r="10" />
                  <line x1="12" y1="8" x2="12" y2="16" />
                  <line x1="8" y1="12" x2="16" y2="12" />
                </svg>
                Create New Story
              </button>
            </div>
          </div>
        </section>

        {/* Enhanced Floating Romantic Elements */}
        <div className="floating-hearts">
          {[...Array(8)].map((_, index) => (
            <div
              key={index}
              className={`floating-heart ${animationStage >= 2 ? 'animate' : ''}`}
              style={{
                left: `${10 + (index * 12)}%`,
                animationDelay: `${index * 0.5}s`,
                animationDuration: `${4 + (index % 3)}s`
              }}
            >
              💕
            </div>
          ))}
        </div>

        {/* Floating Romantic Emojis */}
        <div className="floating-emojis">
          {romanticEmojis.map((emoji, index) => (
            <div
              key={index}
              className={`floating-emoji ${animationStage >= 3 ? 'animate' : ''}`}
              style={{
                left: `${5 + (index * 8)}%`,
                animationDelay: `${index * 0.3 + 2}s`,
                animationDuration: `${6 + (index % 4)}s`
              }}
            >
              {emoji}
            </div>
          ))}
        </div>

        {/* Celebration Confetti */}
        {showCelebration && (
          <div className="celebration-confetti">
            {[...Array(20)].map((_, index) => (
              <div
                key={index}
                className="confetti-piece"
                style={{
                  left: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 2}s`,
                  animationDuration: `${2 + Math.random() * 2}s`,
                  backgroundColor: index % 4 === 0 ? '#ffd700' :
                                   index % 4 === 1 ? '#ff69b4' :
                                   index % 4 === 2 ? '#4c2a85' : '#ffffff'
                }}
              />
            ))}
          </div>
        )}

        {/* Love Rain Effect - Mưa tình yêu */}
        <div className="love-rain-container">
          {rainElements.map((element) => (
            <div
              key={element.id}
              className={`rain-element rain-${element.type}`}
              style={{
                left: `${element.x}%`,
                top: `${element.y}%`,
                transform: `rotate(${element.rotation}deg) scale(${element.scale})`,
                opacity: element.opacity,
                color: element.color,
                fontSize: element.type === 'image' ? 'auto' : `${0.8 + element.scale * 0.5}rem`,
              }}
            >
              {element.type === 'image' ? (
                <img
                  src={element.content}
                  alt="Memory"
                  className="rain-image"
                  style={{
                    width: `${30 + element.scale * 20}px`,
                    height: `${30 + element.scale * 20}px`,
                  }}
                />
              ) : (
                element.content
              )}
            </div>
          ))}
        </div>

        {/* Sparkle Effects */}
        <div className="sparkle-effects">
          {[...Array(15)].map((_, index) => (
            <div
              key={index}
              className={`sparkle ${animationStage >= 1 ? 'animate' : ''}`}
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${2 + Math.random() * 2}s`
              }}
            >
              ✨
            </div>
          ))}
        </div>
      </div>

      {/* QR Code Modal */}
      {showQR && (
        <QRGenerator 
          url={currentUrl}
          title={`Love Story: ${pageData.myName} & ${pageData.partnerName}`}
          onClose={() => setShowQR(false)}
        />
      )}
    </div>
  );
};

export default RomanticDisplay;
