import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { getRomanticPage } from '../../utils/storage';
import { getRandomRomanticEmojis } from '../../utils/loveQuotes';
import ImageGallery from '../ImageGallery/ImageGallery';
import AudioPlayer from '../AudioPlayer/AudioPlayer';
import QRGenerator from '../QRGenerator/QRGenerator';
import './RomanticDisplay.css';

const RomanticDisplay = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [pageData, setPageData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showQR, setShowQR] = useState(false);
  const [animationStage, setAnimationStage] = useState(0);
  const [romanticEmojis, setRomanticEmojis] = useState([]);
  const [showCelebration, setShowCelebration] = useState(false);
  const messageRef = useRef(null);

  useEffect(() => {
    const loadPageData = () => {
      const data = getRomanticPage(id);
      if (data) {
        setPageData(data);
        setRomanticEmojis(getRandomRomanticEmojis(12));

        // Enhanced animation sequence
        setTimeout(() => setAnimationStage(1), 500);
        setTimeout(() => setAnimationStage(2), 1500);
        setTimeout(() => setAnimationStage(3), 2500);
        setTimeout(() => setAnimationStage(4), 3500);
        setTimeout(() => setShowCelebration(true), 4500);
        setTimeout(() => setShowCelebration(false), 7500);
      } else {
        // Page not found, redirect to home
        navigate('/');
      }
      setLoading(false);
    };

    loadPageData();
  }, [id, navigate]);

  useEffect(() => {
    // Typewriter effect for the message
    if (animationStage >= 3 && pageData && messageRef.current) {
      const message = pageData.loveMessage;
      const element = messageRef.current;
      element.textContent = '';
      
      let i = 0;
      const typeWriter = () => {
        if (i < message.length) {
          element.textContent += message.charAt(i);
          i++;
          setTimeout(typeWriter, 50);
        }
      };
      
      setTimeout(typeWriter, 500);
    }
  }, [animationStage, pageData]);

  if (loading) {
    return (
      <div className="romantic-display loading">
        <div className="loading-content">
          <div className="loading-hearts">
            <div className="heart animate-heart-beat"></div>
            <div className="heart animate-heart-beat animate-delay-200"></div>
            <div className="heart animate-heart-beat animate-delay-400"></div>
          </div>
          <p>Loading your love story...</p>
        </div>
      </div>
    );
  }

  if (!pageData) {
    return (
      <div className="romantic-display error">
        <div className="error-content">
          <h2>Love Story Not Found</h2>
          <p>The romantic page you're looking for doesn't exist or has expired.</p>
          <button onClick={() => navigate('/')} className="back-button">
            Create New Love Story
          </button>
        </div>
      </div>
    );
  }

  const currentUrl = window.location.href;

  return (
    <div className="romantic-display">
      {/* Background Audio */}
      {pageData.audio && (
        <AudioPlayer 
          audioData={pageData.audio} 
          autoPlay={animationStage >= 4}
        />
      )}

      <div className="romantic-content">
        {/* Names Section */}
        <section className="names-section">
          <div className="names-container">
            <h1 
              className={`name my-name ${animationStage >= 1 ? 'animate-fall-from-top' : ''}`}
            >
              {pageData.myName}
            </h1>
            <div 
              className={`heart-divider ${animationStage >= 2 ? 'animate-heart-beat animate-pulse-glow' : ''}`}
            >
              💖
            </div>
            <h1 
              className={`name partner-name ${animationStage >= 1 ? 'animate-fall-from-top animate-delay-300' : ''}`}
            >
              {pageData.partnerName}
            </h1>
          </div>
        </section>

        {/* Love Message Section */}
        <section className={`message-section ${animationStage >= 3 ? 'visible' : ''}`}>
          <div className="message-container">
            <div className="message-wrapper">
              <p 
                ref={messageRef}
                className="love-message typewriter-text"
              >
                {/* Text will be added by typewriter effect */}
              </p>
              <div className="message-decoration">
                <span className="star">✨</span>
                <span className="star">✨</span>
                <span className="star">✨</span>
              </div>
            </div>
          </div>
        </section>

        {/* Images Gallery Section */}
        {pageData.images && pageData.images.length > 0 && (
          <section className={`gallery-section ${animationStage >= 4 ? 'visible' : ''}`}>
            <ImageGallery 
              images={pageData.images}
              animationDelay={animationStage >= 4 ? 0 : 1000}
            />
          </section>
        )}

        {/* Action Buttons */}
        <section className={`actions-section ${animationStage >= 4 ? 'visible' : ''}`}>
          <div className="actions-container">
            <button 
              onClick={() => setShowQR(true)}
              className="action-button share-button animate-fade-in-up animate-delay-500"
            >
              <svg className="button-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8" />
                <polyline points="16,6 12,2 8,6" />
                <line x1="12" y1="2" x2="12" y2="15" />
              </svg>
              Share Love Story
            </button>
            
            <button 
              onClick={() => navigate('/')}
              className="action-button create-button animate-fade-in-up animate-delay-700"
            >
              <svg className="button-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="10" />
                <line x1="12" y1="8" x2="12" y2="16" />
                <line x1="8" y1="12" x2="16" y2="12" />
              </svg>
              Create New Story
            </button>
          </div>
        </section>

        {/* Enhanced Floating Romantic Elements */}
        <div className="floating-hearts">
          {[...Array(8)].map((_, index) => (
            <div
              key={index}
              className={`floating-heart ${animationStage >= 2 ? 'animate' : ''}`}
              style={{
                left: `${10 + (index * 12)}%`,
                animationDelay: `${index * 0.5}s`,
                animationDuration: `${4 + (index % 3)}s`
              }}
            >
              💕
            </div>
          ))}
        </div>

        {/* Floating Romantic Emojis */}
        <div className="floating-emojis">
          {romanticEmojis.map((emoji, index) => (
            <div
              key={index}
              className={`floating-emoji ${animationStage >= 3 ? 'animate' : ''}`}
              style={{
                left: `${5 + (index * 8)}%`,
                animationDelay: `${index * 0.3 + 2}s`,
                animationDuration: `${6 + (index % 4)}s`
              }}
            >
              {emoji}
            </div>
          ))}
        </div>

        {/* Celebration Confetti */}
        {showCelebration && (
          <div className="celebration-confetti">
            {[...Array(20)].map((_, index) => (
              <div
                key={index}
                className="confetti-piece"
                style={{
                  left: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 2}s`,
                  animationDuration: `${2 + Math.random() * 2}s`,
                  backgroundColor: index % 4 === 0 ? '#ffd700' :
                                   index % 4 === 1 ? '#ff69b4' :
                                   index % 4 === 2 ? '#4c2a85' : '#ffffff'
                }}
              />
            ))}
          </div>
        )}

        {/* Sparkle Effects */}
        <div className="sparkle-effects">
          {[...Array(15)].map((_, index) => (
            <div
              key={index}
              className={`sparkle ${animationStage >= 1 ? 'animate' : ''}`}
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${2 + Math.random() * 2}s`
              }}
            >
              ✨
            </div>
          ))}
        </div>
      </div>

      {/* QR Code Modal */}
      {showQR && (
        <QRGenerator 
          url={currentUrl}
          title={`Love Story: ${pageData.myName} & ${pageData.partnerName}`}
          onClose={() => setShowQR(false)}
        />
      )}
    </div>
  );
};

export default RomanticDisplay;
