/* Enhanced Galaxy Love Story App - Modern Design System */
@import url('https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap');
@import './styles/variables.css';
@import './styles/utilities.css';
@import './styles/components.css';
@import './styles/animations.css';

/* Enhanced Reset and Base Styles */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
  line-height: var(--line-height-normal);
}

body {
  font-family: 'Poppins', system-ui, -apple-system, sans-serif;
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-relaxed);
  color: var(--text-primary);
  background: var(--bg-primary);
  min-height: 100vh;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Enhanced Galaxy Background */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
    linear-gradient(135deg, var(--galaxy-deep-blue) 0%, var(--galaxy-dark-blue) 100%);
  z-index: -1;
  pointer-events: none;
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Dancing Script', cursive;
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--text-accent);
  margin-bottom: var(--space-4);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

h1 {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-extrabold);
}
h2 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
}
h3 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
}
h4 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
}
h5 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}
h6 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
}

p {
  margin-bottom: var(--space-4);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
}

/* Text Utilities */
.text-gradient {
  background: linear-gradient(135deg, var(--galaxy-gold), var(--galaxy-pink));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-glow {
  text-shadow:
    0 0 10px currentColor,
    0 0 20px rgba(255, 255, 255, 0.5),
    0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Enhanced Links */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: all var(--transition-fast);
  position: relative;
}

a:hover {
  color: var(--text-primary);
  text-shadow: 0 0 8px currentColor;
}

a:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Enhanced Button Reset */
button {
  /* Reset handled by component classes */
  all: unset;
  box-sizing: border-box;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Focus styles for accessibility */
button:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Enhanced Form Elements - Use component classes instead */
input, textarea, select {
  /* Base styles handled by .input component class */
  font-family: inherit;
}

/* Screen Reader Only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Theme Toggle */
.theme-toggle {
  position: fixed;
  top: var(--space-4);
  right: var(--space-4);
  z-index: var(--z-fixed);
  background: var(--bg-glass);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full);
  padding: var(--space-2);
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
}

.theme-toggle:hover {
  background: var(--bg-glass-light);
  transform: scale(1.1);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--galaxy-gold), var(--galaxy-pink));
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--galaxy-pink), var(--galaxy-gold));
}

/* Selection Styling */
::selection {
  background: var(--galaxy-gold);
  color: var(--galaxy-deep-blue);
}

::-moz-selection {
  background: var(--galaxy-gold);
  color: var(--galaxy-deep-blue);
}

/* Print Styles */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  body {
    font-size: 12pt;
    line-height: 1.5;
  }

  h1, h2, h3, h4, h5, h6 {
    page-break-after: avoid;
  }
}
