import { useState, useEffect } from 'react';
import './LoadingScreen.css';

const LoadingScreen = ({ onComplete }) => {
  const [progress, setProgress] = useState(0);
  const [currentText, setCurrentText] = useState('Initializing...');
  const [isComplete, setIsComplete] = useState(false);

  const loadingTexts = [
    'Initializing love magic...',
    'Preparing romantic atmosphere...',
    'Loading beautiful memories...',
    'Creating magical moments...',
    'Sprinkling love dust...',
    'Almost ready for your love story...'
  ];

  useEffect(() => {
    const duration = 3000; // 3 seconds
    const interval = 50; // Update every 50ms
    const steps = duration / interval;
    const progressStep = 100 / steps;

    let currentStep = 0;
    const timer = setInterval(() => {
      currentStep++;
      const newProgress = Math.min(currentStep * progressStep, 100);
      setProgress(newProgress);

      // Update text based on progress
      const textIndex = Math.floor((newProgress / 100) * (loadingTexts.length - 1));
      setCurrentText(loadingTexts[textIndex]);

      if (newProgress >= 100) {
        clearInterval(timer);
        setIsComplete(true);
        setTimeout(() => {
          onComplete && onComplete();
        }, 500);
      }
    }, interval);

    return () => clearInterval(timer);
  }, [onComplete]);

  return (
    <div className={`loading-screen ${isComplete ? 'complete' : ''}`}>
      <div className="loading-background">
        {/* Animated stars */}
        {[...Array(50)].map((_, index) => (
          <div
            key={index}
            className="star"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>

      <div className="loading-content">
        {/* Logo/Brand */}
        <div className="loading-logo">
          <div className="logo-heart">💖</div>
          <h1 className="logo-text">Love Story</h1>
        </div>

        {/* Progress Circle */}
        <div className="progress-container">
          <svg className="progress-circle" viewBox="0 0 120 120">
            <circle
              className="progress-track"
              cx="60"
              cy="60"
              r="54"
              fill="none"
              stroke="rgba(255, 255, 255, 0.1)"
              strokeWidth="4"
            />
            <circle
              className="progress-bar"
              cx="60"
              cy="60"
              r="54"
              fill="none"
              stroke="url(#gradient)"
              strokeWidth="4"
              strokeLinecap="round"
              style={{
                strokeDasharray: `${2 * Math.PI * 54}`,
                strokeDashoffset: `${2 * Math.PI * 54 * (1 - progress / 100)}`
              }}
            />
            <defs>
              <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="var(--galaxy-gold)" />
                <stop offset="50%" stopColor="var(--galaxy-pink)" />
                <stop offset="100%" stopColor="var(--galaxy-purple)" />
              </linearGradient>
            </defs>
          </svg>
          
          <div className="progress-content">
            <div className="progress-percentage">{Math.round(progress)}%</div>
            <div className="progress-hearts">
              <span className="heart-1">💕</span>
              <span className="heart-2">💖</span>
              <span className="heart-3">💝</span>
            </div>
          </div>
        </div>

        {/* Loading Text */}
        <div className="loading-text">
          <p className="loading-message">{currentText}</p>
          <div className="loading-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>

        {/* Floating Elements */}
        <div className="floating-elements">
          {['✨', '💫', '🌟', '💖', '💕', '💝'].map((emoji, index) => (
            <div
              key={index}
              className="floating-element"
              style={{
                left: `${15 + index * 12}%`,
                animationDelay: `${index * 0.5}s`,
                animationDuration: `${3 + index * 0.5}s`
              }}
            >
              {emoji}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;
