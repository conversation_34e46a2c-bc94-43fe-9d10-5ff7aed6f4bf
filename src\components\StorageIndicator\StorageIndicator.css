.storage-indicator {
  position: fixed;
  bottom: var(--space-6);
  left: var(--space-6);
  z-index: 100;
}

.storage-toggle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(26, 26, 58, 0.9);
  backdrop-filter: blur(10px);
  border: 2px solid;
  color: var(--galaxy-white);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2px;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-lg);
  position: relative;
}

.storage-toggle.good {
  border-color: #22c55e;
}

.storage-toggle.warning {
  border-color: #f59e0b;
}

.storage-toggle.critical {
  border-color: #ef4444;
  animation: storageWarning 2s ease-in-out infinite;
}

@keyframes storageWarning {
  0%, 100% {
    box-shadow: var(--shadow-lg);
  }
  50% {
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.5);
  }
}

.storage-toggle:hover {
  transform: scale(1.1);
}

.storage-toggle svg {
  width: 16px;
  height: 16px;
  stroke-width: 2;
}

.storage-percentage {
  font-size: 8px;
  font-weight: 600;
  line-height: 1;
}

.storage-panel {
  position: absolute;
  bottom: 60px;
  left: 0;
  width: 280px;
  background: rgba(26, 26, 58, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: var(--radius-xl);
  padding: var(--space-4);
  box-shadow: var(--shadow-xl);
  animation: slideInFromBottom 0.3s ease-out;
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.storage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
}

.storage-header h4 {
  margin: 0;
  color: var(--galaxy-gold);
  font-size: var(--font-size-base);
  font-weight: 600;
}

.close-storage {
  background: none;
  border: none;
  color: var(--galaxy-silver);
  font-size: 18px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all var(--transition-fast);
}

.close-storage:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--galaxy-white);
}

.storage-bar {
  width: 100%;
  height: 8px;
  background: var(--galaxy-dark-blue);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: var(--space-4);
  position: relative;
}

.storage-fill {
  height: 100%;
  border-radius: var(--radius-full);
  transition: width 0.5s ease-out;
  position: relative;
}

.storage-fill.good {
  background: linear-gradient(90deg, #22c55e, #16a34a);
}

.storage-fill.warning {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.storage-fill.critical {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.storage-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: storageShine 2s ease-in-out infinite;
}

@keyframes storageShine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.storage-details {
  margin-bottom: var(--space-4);
}

.storage-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
}

.stat-label {
  color: var(--galaxy-silver);
  font-size: var(--font-size-sm);
}

.stat-value {
  color: var(--galaxy-white);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.storage-warning {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: var(--radius-md);
  padding: var(--space-3);
  margin-bottom: var(--space-4);
  text-align: center;
}

.storage-warning p {
  margin: 0 0 var(--space-2) 0;
  color: #ef4444;
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.cleanup-button {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  border: 1px solid #ef4444;
  border-radius: var(--radius-md);
  padding: var(--space-2) var(--space-3);
  color: var(--galaxy-white);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.cleanup-button:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-1px);
}

.storage-info {
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.3);
  border-radius: var(--radius-md);
  padding: var(--space-3);
  margin-bottom: var(--space-4);
}

.storage-info p {
  margin: 0;
  color: #f59e0b;
  font-size: var(--font-size-xs);
  text-align: center;
}

.storage-tips {
  border-top: 1px solid rgba(255, 215, 0, 0.2);
  padding-top: var(--space-3);
}

.storage-tips h5 {
  margin: 0 0 var(--space-2) 0;
  color: var(--galaxy-gold);
  font-size: var(--font-size-sm);
  font-weight: 600;
}

.storage-tips ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.storage-tips li {
  color: var(--galaxy-silver);
  font-size: var(--font-size-xs);
  margin-bottom: var(--space-1);
  padding-left: var(--space-3);
  position: relative;
}

.storage-tips li:before {
  content: '•';
  color: var(--galaxy-gold);
  position: absolute;
  left: 0;
  top: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .storage-indicator {
    bottom: var(--space-4);
    left: var(--space-4);
  }
  
  .storage-toggle {
    width: 45px;
    height: 45px;
  }
  
  .storage-toggle svg {
    width: 14px;
    height: 14px;
  }
  
  .storage-percentage {
    font-size: 7px;
  }
  
  .storage-panel {
    width: 260px;
    padding: var(--space-3);
  }
}

@media (max-width: 480px) {
  .storage-panel {
    width: 240px;
    left: -var(--space-4);
  }
  
  .storage-tips li {
    font-size: 10px;
  }
}

/* Accessibility */
.storage-toggle:focus,
.close-storage:focus,
.cleanup-button:focus {
  outline: 2px solid var(--galaxy-gold);
  outline-offset: 2px;
}

/* Performance optimizations */
.storage-panel {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
}

@media (prefers-reduced-motion: reduce) {
  .storage-panel,
  .storage-fill::after {
    animation: none !important;
  }
  
  .storage-toggle:hover {
    transform: none !important;
  }
}
