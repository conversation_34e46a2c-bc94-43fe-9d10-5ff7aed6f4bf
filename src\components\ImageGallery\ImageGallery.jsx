import React, { useState, useEffect, useCallback } from 'react';
import './ImageGallery.css';

const ImageGallery = ({ images, animationStage = 0, enableCascade = false }) => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  const [imagesLoaded, setImagesLoaded] = useState(false);
  const [cascadeStage, setCascadeStage] = useState(0);

  useEffect(() => {
    // Preload images
    const imagePromises = images.map((image) => {
      return new Promise((resolve) => {
        const img = new Image();
        img.onload = resolve;
        img.onerror = resolve; // Still resolve on error to not block the gallery
        img.src = image.base64 || image.preview;
      });
    });

    Promise.all(imagePromises).then(() => {
      setImagesLoaded(true);

      if (enableCascade) {
        // Start cascading animation for each image
        images.forEach((_, index) => {
          setTimeout(() => {
            setCascadeStage(prev => Math.max(prev, index + 1));
          }, index * 250); // 0.25s delay between each image
        });
      } else {
        // Show all images at once for non-cascade mode
        setCascadeStage(images.length);
      }
    });
  }, [images, enableCascade]);

  const openLightbox = (image, index) => {
    setSelectedImage(image);
    setCurrentIndex(index);
    setIsLightboxOpen(true);
    document.body.style.overflow = 'hidden';
  };

  const closeLightbox = () => {
    setIsLightboxOpen(false);
    setSelectedImage(null);
    document.body.style.overflow = 'auto';
  };

  const goToNext = useCallback(() => {
    const nextIndex = (currentIndex + 1) % images.length;
    setCurrentIndex(nextIndex);
    setSelectedImage(images[nextIndex]);
  }, [currentIndex, images]);

  const goToPrevious = useCallback(() => {
    const prevIndex = currentIndex === 0 ? images.length - 1 : currentIndex - 1;
    setCurrentIndex(prevIndex);
    setSelectedImage(images[prevIndex]);
  }, [currentIndex, images]);

  const goToImage = (index) => {
    setCurrentIndex(index);
    setSelectedImage(images[index]);
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!isLightboxOpen) return;

      switch (e.key) {
        case 'Escape':
          closeLightbox();
          break;
        case 'ArrowLeft':
          goToPrevious();
          break;
        case 'ArrowRight':
          goToNext();
          break;
        default:
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isLightboxOpen, goToNext, goToPrevious]);

  if (!images || images.length === 0) {
    return null;
  }

  return (
    <>
      <div className="image-gallery">
        <div className="gallery-grid">
          {images.map((image, index) => {
            const shouldShow = enableCascade ? cascadeStage > index : imagesLoaded;
            const animationClass = enableCascade ? 'cascade-fall-in' : 'animate-fade-in-rotate';

            return (
              <div
                key={image.id || index}
                className={`gallery-item ${shouldShow ? animationClass : 'cascade-hidden'}`}
                style={{
                  animationDelay: enableCascade ? '0s' : `${index * 0.2}s`
                }}
                onClick={() => openLightbox(image, index)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    openLightbox(image, index);
                  }
                }}
                aria-label={`View image ${index + 1} of ${images.length}`}
              >
                <img
                  src={image.base64 || image.preview}
                  alt={`Memory ${index + 1}`}
                  className="gallery-image"
                  loading="lazy"
                />
                <div className="gallery-overlay">
                  <div className="overlay-content">
                    <svg className="expand-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <polyline points="15,3 21,3 21,9" />
                      <polyline points="9,21 3,21 3,15" />
                      <line x1="21" y1="3" x2="14" y2="10" />
                      <line x1="3" y1="21" x2="10" y2="14" />
                    </svg>
                    <span className="overlay-text">View Full Size</span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Lightbox Modal */}
      {isLightboxOpen && selectedImage && (
        <div className="lightbox-overlay" onClick={closeLightbox}>
          <div className="lightbox-container" onClick={(e) => e.stopPropagation()}>
            {/* Close Button */}
            <button
              className="lightbox-close"
              onClick={closeLightbox}
              aria-label="Close lightbox"
            >
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <line x1="18" y1="6" x2="6" y2="18" />
                <line x1="6" y1="6" x2="18" y2="18" />
              </svg>
            </button>

            {/* Navigation Arrows */}
            {images.length > 1 && (
              <>
                <button
                  className="lightbox-nav lightbox-prev"
                  onClick={goToPrevious}
                  aria-label="Previous image"
                >
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <polyline points="15,18 9,12 15,6" />
                  </svg>
                </button>
                <button
                  className="lightbox-nav lightbox-next"
                  onClick={goToNext}
                  aria-label="Next image"
                >
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <polyline points="9,18 15,12 9,6" />
                  </svg>
                </button>
              </>
            )}

            {/* Main Image */}
            <div className="lightbox-image-container">
              <img
                src={selectedImage.base64 || selectedImage.preview}
                alt={`Memory ${currentIndex + 1}`}
                className="lightbox-image"
              />
            </div>

            {/* Image Counter */}
            <div className="lightbox-counter">
              {currentIndex + 1} / {images.length}
            </div>

            {/* Thumbnail Navigation */}
            {images.length > 1 && (
              <div className="lightbox-thumbnails">
                {images.map((image, index) => (
                  <button
                    key={image.id || index}
                    className={`thumbnail ${index === currentIndex ? 'active' : ''}`}
                    onClick={() => goToImage(index)}
                    aria-label={`Go to image ${index + 1}`}
                  >
                    <img
                      src={image.base64 || image.preview}
                      alt={`Thumbnail ${index + 1}`}
                    />
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default ImageGallery;
