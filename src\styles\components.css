/* Modern Component Styles */

/* Button Components */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-none);
  text-decoration: none;
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
  position: relative;
  overflow: hidden;
  user-select: none;
  transform: translateZ(0);
  will-change: transform, box-shadow;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.3);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Button Variants */
.btn-primary {
  background: linear-gradient(135deg, var(--galaxy-gold), var(--galaxy-light-gold));
  color: var(--galaxy-deep-blue);
  border-color: var(--galaxy-gold);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--galaxy-light-gold), var(--galaxy-gold));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), var(--shadow-glow);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: linear-gradient(135deg, var(--galaxy-purple), var(--galaxy-pink));
  color: var(--galaxy-white);
  border-color: var(--galaxy-pink);
  box-shadow: var(--shadow-md);
}

.btn-secondary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--galaxy-pink), var(--galaxy-purple));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), var(--shadow-glow-pink);
}

.btn-outline {
  background: transparent;
  color: var(--galaxy-gold);
  border-color: var(--galaxy-gold);
  backdrop-filter: blur(10px);
}

.btn-outline:hover:not(:disabled) {
  background: var(--galaxy-gold);
  color: var(--galaxy-deep-blue);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), var(--shadow-glow);
}

.btn-ghost {
  background: rgba(255, 255, 255, 0.1);
  color: var(--galaxy-white);
  border-color: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.btn-ghost:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

/* Button Sizes */
.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--font-size-sm);
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--font-size-lg);
}

.btn-xl {
  padding: var(--space-5) var(--space-10);
  font-size: var(--font-size-xl);
}

/* Input Components */
.input-group {
  position: relative;
  margin-bottom: var(--space-4);
}

.input {
  width: 100%;
  padding: var(--space-4) var(--space-4);
  font-size: var(--font-size-base);
  color: var(--text-primary);
  background: var(--bg-glass);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
  transition: all var(--duration-200) var(--ease-out);
  outline: none;
}

.input:focus {
  border-color: var(--galaxy-gold);
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.2);
  transform: translateY(-1px);
}

.input:invalid {
  border-color: var(--color-error);
}

.input:valid {
  border-color: var(--color-success);
}

/* Floating Label */
.input-floating {
  padding-top: var(--space-6);
  padding-bottom: var(--space-2);
}

.label-floating {
  position: absolute;
  left: var(--space-4);
  top: var(--space-4);
  font-size: var(--font-size-base);
  color: var(--text-muted);
  pointer-events: none;
  transition: all var(--duration-200) var(--ease-out);
  transform-origin: left top;
}

.input-floating:focus + .label-floating,
.input-floating:not(:placeholder-shown) + .label-floating {
  transform: translateY(-12px) scale(0.85);
  color: var(--galaxy-gold);
}

/* Card Components */
.card {
  background: var(--bg-glass);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  transition: all var(--duration-300) var(--ease-out);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl), var(--shadow-glow);
  border-color: rgba(255, 215, 0, 0.3);
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

/* Modal Components */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  z-index: var(--z-modal-backdrop);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-4);
  animation: fadeIn var(--duration-300) var(--ease-out);
}

.modal {
  background: var(--bg-glass);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-2xl);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-2xl);
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  animation: modalSlideIn var(--duration-300) var(--ease-back);
}

/* Toast Components */
.toast {
  position: fixed;
  top: var(--space-4);
  right: var(--space-4);
  background: var(--bg-glass);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(20px);
  padding: var(--space-4) var(--space-6);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-toast);
  animation: toastSlideIn var(--duration-300) var(--ease-out);
}

.toast-success {
  border-color: var(--color-success);
  box-shadow: var(--shadow-lg), 0 0 20px rgba(16, 185, 129, 0.3);
}

.toast-error {
  border-color: var(--color-error);
  box-shadow: var(--shadow-lg), 0 0 20px rgba(239, 68, 68, 0.3);
}

/* Loading Components */
.spinner {
  width: 24px;
  height: 24px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid var(--galaxy-gold);
  border-radius: 50%;
  animation: spin var(--duration-1000) linear infinite;
}

.skeleton {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.1) 25%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.1) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
  border-radius: var(--radius-md);
}

/* Progress Components */
.progress {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--galaxy-gold), var(--galaxy-pink));
  border-radius: var(--radius-full);
  transition: width var(--duration-300) var(--ease-out);
  position: relative;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: progressShine 2s infinite;
}

/* Badge Components */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
}

.badge-primary {
  background: var(--galaxy-gold);
  color: var(--galaxy-deep-blue);
}

.badge-secondary {
  background: var(--galaxy-pink);
  color: var(--galaxy-white);
}

.badge-outline {
  background: transparent;
  border: 1px solid var(--galaxy-gold);
  color: var(--galaxy-gold);
}

/* Divider Components */
.divider {
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  margin: var(--space-8) 0;
}

.divider-vertical {
  width: 1px;
  height: 100%;
  background: linear-gradient(
    180deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  margin: 0 var(--space-4);
}
