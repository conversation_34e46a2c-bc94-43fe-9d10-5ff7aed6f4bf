/* Enhanced App Styles */
.app {
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
}

.app-initializing {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-lg);
}

.main-content {
  flex: 1;
  position: relative;
  z-index: 1;
  padding-top: 70px; /* Account for fixed navigation */
}

.page-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  font-size: var(--font-size-2xl);
  color: var(--text-accent);
  font-family: 'Dancing Script', cursive;
  font-weight: var(--font-weight-bold);
}

/* Enhanced scrollbar for the app */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--galaxy-gold), var(--galaxy-pink));
  border-radius: var(--radius-full);
  border: 2px solid var(--bg-secondary);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--galaxy-pink), var(--galaxy-gold));
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles for better accessibility */
*:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Performance optimizations */
.app,
.main-content {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .main-content {
    padding-top: 60px; /* Smaller navigation on mobile */
  }
}

/* Print styles */
@media print {
  .app {
    background: white !important;
    color: black !important;
  }

  .main-content {
    padding-top: 0;
  }
}

/* Main layout styles */
.main-content {
  position: relative;
  z-index: 1;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Page transitions */
.page-enter {
  opacity: 0;
  transform: translateX(100%);
}

.page-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 0.5s ease-out, transform 0.5s ease-out;
}

.page-exit {
  opacity: 1;
  transform: translateX(0);
}

.page-exit-active {
  opacity: 0;
  transform: translateX(-100%);
  transition: opacity 0.5s ease-in, transform 0.5s ease-in;
}

/* Responsive container */
.app-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
  width: 100%;
}

@media (max-width: 768px) {
  .app-container {
    padding: 0 var(--space-2);
  }
}
