/* Enhanced Landing Page */
.landing-page {
  min-height: 100vh;
  padding: var(--space-8) 0;
  position: relative;
  z-index: 1;
}

/* Enhanced Hero Section */
.hero-content {
  max-width: 900px;
  margin: 0 auto;
  padding: var(--space-12) 0;
}

.hero-badge {
  margin-bottom: var(--space-6);
  animation-delay: 0.1s;
}

.hero-title {
  font-size: clamp(2.5rem, 6vw, 4rem);
  font-weight: var(--font-weight-extrabold);
  margin-bottom: var(--space-6);
  line-height: var(--line-height-tight);
  animation-delay: 0.2s;
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
  margin-bottom: var(--space-8);
  line-height: var(--line-height-relaxed);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  animation-delay: 0.3s;
}

.hero-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: var(--space-4);
  margin-top: var(--space-8);
  animation-delay: 0.4s;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4);
  background: var(--bg-glass);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
  text-align: center;
}

.feature-item:hover {
  transform: translateY(-4px);
  border-color: rgba(255, 215, 0, 0.3);
  box-shadow: var(--shadow-glow);
}

.feature-icon {
  font-size: var(--font-size-2xl);
  margin-bottom: var(--space-1);
}

.feature-item span {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

/* Enhanced Form Styles */
.romantic-form {
  max-width: 900px;
  margin: 0 auto;
  animation-delay: 0.5s;
}

.form-header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.form-header h2 {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--space-3);
  color: var(--text-accent);
}

.form-header p {
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
}

.form-section {
  margin-bottom: var(--space-10);
}

.form-section h3 {
  font-size: var(--font-size-xl);
  margin-bottom: var(--space-6);
  color: var(--text-accent);
  text-align: center;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-6);
}

.message-section {
  max-width: 100%;
}

.message-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: var(--space-4);
}

.message-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--space-3);
  font-size: var(--font-size-sm);
}

.character-count {
  font-weight: var(--font-weight-medium);
}

.message-tips {
  color: var(--color-info);
  font-style: italic;
}

.form-actions {
  text-align: center;
  margin-top: var(--space-8);
}

.form-disclaimer {
  margin-top: var(--space-4);
  font-size: var(--font-size-sm);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Progress Overlay */
.progress-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

.landing-header {
  text-align: center;
  margin-bottom: var(--space-12);
}

.landing-header h1 {
  font-size: clamp(2rem, 5vw, 3.5rem);
  margin-bottom: var(--space-4);
  background: linear-gradient(135deg, var(--galaxy-gold), var(--galaxy-white));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(255, 215, 0, 0.3);
}

.landing-header p {
  font-size: var(--font-size-lg);
  color: var(--galaxy-silver);
  max-width: 600px;
  margin: 0 auto;
}

/* Inspiration Section */
.inspiration-section {
  margin-top: var(--space-8);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.inspiration-toggle {
  background: rgba(26, 26, 58, 0.6);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: var(--radius-full);
  padding: var(--space-3) var(--space-6);
  color: var(--galaxy-gold);
  font-size: var(--font-size-sm);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin: 0 auto;
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
}

.inspiration-toggle:hover {
  background: rgba(255, 215, 0, 0.1);
  border-color: var(--galaxy-gold);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.2);
}

.inspiration-toggle svg {
  width: 16px;
  height: 16px;
  stroke-width: 2;
  transition: transform var(--transition-fast);
}

.inspiration-section .inspiration-content {
  margin-top: var(--space-4);
}

.inspiration-toggle[aria-expanded="true"] svg {
  transform: rotate(90deg);
}

.inspiration-content {
  background: rgba(26, 26, 58, 0.4);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 215, 0, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  margin-top: var(--space-4);
}

.love-quote {
  text-align: center;
  margin-bottom: var(--space-4);
}

.love-quote blockquote {
  font-size: var(--font-size-lg);
  font-style: italic;
  color: var(--galaxy-white);
  margin: 0 0 var(--space-3) 0;
  line-height: 1.6;
  position: relative;
}

.love-quote blockquote::before {
  content: '"';
  font-size: 3rem;
  color: var(--galaxy-gold);
  position: absolute;
  left: -20px;
  top: -10px;
  font-family: serif;
}

.love-quote cite {
  font-size: var(--font-size-sm);
  color: var(--galaxy-silver);
  font-style: normal;
}

.inspiration-actions {
  display: flex;
  justify-content: center;
}

.refresh-quote-btn {
  background: linear-gradient(135deg, var(--galaxy-purple), var(--galaxy-light-purple));
  border: 1px solid var(--galaxy-gold);
  border-radius: var(--radius-md);
  padding: var(--space-2) var(--space-4);
  color: var(--galaxy-white);
  font-size: var(--font-size-sm);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  transition: all var(--transition-fast);
}

.refresh-quote-btn:hover {
  background: linear-gradient(135deg, var(--galaxy-light-purple), var(--galaxy-purple));
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.refresh-quote-btn svg {
  width: 14px;
  height: 14px;
  stroke-width: 2;
}

/* Form Styles */
.romantic-form {
  max-width: 800px;
  margin: 0 auto;
  background: rgba(26, 26, 58, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 215, 0, 0.2);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-xl);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-6);
  margin-bottom: var(--space-6);
}

.form-group {
  margin-bottom: var(--space-6);
}

.form-group label {
  display: block;
  margin-bottom: var(--space-2);
  font-weight: 600;
  color: var(--galaxy-gold);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: var(--space-4);
  border: 2px solid var(--galaxy-dark-blue);
  border-radius: var(--radius-md);
  background: rgba(26, 26, 58, 0.8);
  color: var(--galaxy-white);
  font-size: var(--font-size-base);
  transition: all var(--transition-fast);
  resize: vertical;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--galaxy-gold);
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
  background: rgba(26, 26, 58, 0.9);
}

.form-group input.error,
.form-group textarea.error {
  border-color: #ff4444;
  box-shadow: 0 0 0 3px rgba(255, 68, 68, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: var(--galaxy-silver);
  opacity: 0.7;
}

/* Message Header and Footer */
.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
  flex-wrap: wrap;
  gap: var(--space-2);
}

.template-generator-btn {
  background: linear-gradient(135deg, var(--galaxy-pink), #ff1493);
  border: 1px solid var(--galaxy-gold);
  border-radius: var(--radius-md);
  padding: var(--space-2) var(--space-3);
  color: var(--galaxy-white);
  font-size: var(--font-size-xs);
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--space-1);
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.template-generator-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #ff1493, var(--galaxy-pink));
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 105, 180, 0.4);
}

.template-generator-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.template-generator-btn svg {
  width: 14px;
  height: 14px;
  stroke-width: 2;
}

.loading-spinner-small {
  width: 12px;
  height: 12px;
  border: 2px solid transparent;
  border-top: 2px solid var(--galaxy-white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.message-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--space-2);
  flex-wrap: wrap;
  gap: var(--space-2);
}

/* Character Count */
.character-count {
  font-size: var(--font-size-xs);
  color: var(--galaxy-silver);
}

.character-count .over-limit {
  color: #ff4444;
}

.message-tips {
  font-size: var(--font-size-xs);
  color: var(--galaxy-gold);
  font-style: italic;
  opacity: 0.8;
}

/* File Upload Styles */
.file-drop-zone {
  border: 2px dashed var(--galaxy-dark-blue);
  border-radius: var(--radius-lg);
  padding: var(--space-8);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  background: rgba(26, 26, 58, 0.3);
}

.file-drop-zone:hover,
.file-drop-zone.drag-over {
  border-color: var(--galaxy-gold);
  background: rgba(255, 215, 0, 0.05);
  transform: translateY(-2px);
}

.drop-zone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-3);
}

.upload-icon {
  width: 48px;
  height: 48px;
  color: var(--galaxy-gold);
  stroke-width: 1.5;
}

.drop-zone-content p {
  margin: 0;
  color: var(--galaxy-white);
  font-weight: 500;
}

.file-info {
  font-size: var(--font-size-xs);
  color: var(--galaxy-silver) !important;
  opacity: 0.8;
}

.file-tip {
  font-size: var(--font-size-xs);
  color: var(--galaxy-gold) !important;
  opacity: 0.9;
  font-style: italic;
  margin-top: var(--space-1);
}

/* Audio Upload */
.audio-upload {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.upload-button {
  padding: var(--space-3) var(--space-6);
  background: linear-gradient(135deg, var(--galaxy-purple), var(--galaxy-light-purple));
  border: 2px solid var(--galaxy-gold);
  border-radius: var(--radius-md);
  color: var(--galaxy-white);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.upload-button:hover {
  background: linear-gradient(135deg, var(--galaxy-light-purple), var(--galaxy-purple));
  box-shadow: var(--shadow-glow);
  transform: translateY(-2px);
}

/* Image Previews */
.image-previews {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: var(--space-4);
  margin-top: var(--space-4);
}

.image-preview {
  position: relative;
  background: rgba(26, 26, 58, 0.8);
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: 2px solid var(--galaxy-dark-blue);
  transition: all var(--transition-normal);
}

.image-preview:hover {
  border-color: var(--galaxy-gold);
  transform: translateY(-4px);
  box-shadow: var(--shadow-glow);
}

.image-preview img {
  width: 100%;
  height: 120px;
  object-fit: cover;
  display: block;
}

.image-info {
  padding: var(--space-2);
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.image-name {
  font-size: var(--font-size-xs);
  color: var(--galaxy-white);
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.image-size {
  font-size: var(--font-size-xs);
  color: var(--galaxy-silver);
}

.remove-button {
  position: absolute;
  top: var(--space-2);
  right: var(--space-2);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(255, 68, 68, 0.9);
  border: none;
  color: white;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.remove-button:hover {
  background: #ff4444;
  transform: scale(1.1);
}

/* Audio Preview */
.audio-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  background: rgba(26, 26, 58, 0.8);
  border: 2px solid var(--galaxy-dark-blue);
  border-radius: var(--radius-lg);
  margin-top: var(--space-4);
}

.audio-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.audio-icon {
  width: 24px;
  height: 24px;
  color: var(--galaxy-gold);
  stroke-width: 2;
}

.audio-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.audio-name {
  font-size: var(--font-size-sm);
  color: var(--galaxy-white);
  font-weight: 500;
}

.audio-size {
  font-size: var(--font-size-xs);
  color: var(--galaxy-silver);
}

/* Error Messages */
.error-message {
  display: block;
  margin-top: var(--space-2);
  font-size: var(--font-size-xs);
  color: #ff4444;
  font-weight: 500;
}

.submit-error {
  text-align: center;
  margin-bottom: var(--space-4);
  padding: var(--space-3);
  border-radius: var(--radius-md);
  font-weight: 500;
  animation: messageSlideIn 0.3s ease-out;
}

.submit-error[data-type="error"] {
  background: rgba(255, 68, 68, 0.1);
  border: 1px solid rgba(255, 68, 68, 0.3);
  color: #ff4444;
}

.submit-error[data-type="success"] {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  color: #22c55e;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Form Actions */
.form-actions {
  text-align: center;
  margin-top: var(--space-8);
}

.submit-button {
  padding: var(--space-4) var(--space-8);
  font-size: var(--font-size-lg);
  font-weight: 600;
  background: linear-gradient(135deg, var(--galaxy-purple), var(--galaxy-light-purple));
  border: 2px solid var(--galaxy-gold);
  border-radius: var(--radius-lg);
  color: var(--galaxy-white);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  min-width: 200px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
}

.submit-button:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--galaxy-light-purple), var(--galaxy-purple));
  box-shadow: var(--shadow-glow);
  transform: translateY(-2px);
}

.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid var(--galaxy-white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Utility Classes */
.hidden {
  display: none !important;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .hero-content {
    padding: var(--space-8) var(--space-4);
  }

  .hero-title {
    font-size: clamp(2rem, 8vw, 3rem);
  }

  .hero-subtitle {
    font-size: var(--font-size-lg);
  }

  .hero-features {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-3);
  }

  .romantic-form {
    margin: 0 var(--space-4);
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .message-header {
    justify-content: center;
  }

  .message-footer {
    flex-direction: column;
    align-items: center;
    gap: var(--space-2);
    text-align: center;
  }

  .image-previews {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: var(--space-3);
  }

  .audio-upload {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }

  .inspiration-section {
    margin: var(--space-6) var(--space-4) 0;
  }

  .inspiration-content {
    padding: var(--space-4);
  }
}

@media (max-width: 480px) {
  .hero-content {
    padding: var(--space-6) var(--space-2);
  }

  .hero-title {
    font-size: clamp(1.75rem, 10vw, 2.5rem);
  }

  .hero-subtitle {
    font-size: var(--font-size-base);
  }

  .hero-features {
    grid-template-columns: 1fr;
    gap: var(--space-2);
  }

  .feature-item {
    padding: var(--space-3);
  }

  .romantic-form {
    margin: 0 var(--space-2);
  }

  .form-header h2 {
    font-size: var(--font-size-2xl);
  }

  .form-section h3 {
    font-size: var(--font-size-lg);
  }

  .image-previews {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }

  .file-drop-zone {
    padding: var(--space-6);
  }

  .upload-icon {
    width: 36px;
    height: 36px;
  }
}

/* Accessibility and Performance */
@media (prefers-reduced-motion: reduce) {
  .feature-item,
  .btn,
  .hero-content > * {
    transition: none;
    animation: none;
  }

  .reveal-up,
  .reveal-down,
  .reveal-left,
  .reveal-right {
    opacity: 1;
    transform: none;
  }
}

@media (prefers-contrast: high) {
  .feature-item,
  .romantic-form {
    border-width: 2px;
    border-color: var(--color-primary);
  }

  .btn {
    border-width: 2px;
  }
}
