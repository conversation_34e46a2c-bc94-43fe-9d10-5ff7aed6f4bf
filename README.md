# 💖 Love Story Creator

A beautiful, romantic web application that allows couples to create personalized love stories with photos, music, and heartfelt messages. Share your romantic experience through a unique QR code that your partner can scan to view your creation.

## ✨ Features

### 🎨 **Visual Design**
- **Galaxy/Space Theme**: Stunning animated starry background with twinkling stars and floating particles
- **Smooth Animations**: CSS keyframe animations with staggered timing for professional 60fps performance
- **Responsive Design**: Mobile-first approach using CSS Grid and Flexbox
- **Color Scheme**: Deep space blues and purples with golden/white accents

### 📝 **Core Functionality**
- **Personal Information**: Input fields for both partners' names
- **Love Messages**: Textarea with character limit display (up to 1000 characters)
- **Image Gallery**: Upload multiple images (JPG, PNG, GIF) with preview thumbnails
- **Background Music**: Audio file upload (MP3, WAV) with playback controls
- **Form Validation**: Real-time validation with helpful error messages

### 🎭 **Animations & Effects**
- **Text Animations**: Names falling from top with staggered timing
- **Typewriter Effect**: Love message appears with typewriter animation
- **Image Entrance**: Photos appear with fade-in and gentle rotation effects
- **Floating Hearts**: Romantic particle effects throughout the experience
- **Smooth Transitions**: Seamless navigation between form and results

### 🎵 **Audio Experience**
- **Background Music**: Auto-playing music with user permission
- **Audio Controls**: Play/pause/volume controls with minimizable interface
- **File Support**: MP3 and WAV format support with file validation

### 🖼️ **Image Gallery**
- **Lightbox Functionality**: Full-screen image viewing with navigation
- **Thumbnail Navigation**: Easy browsing through uploaded images
- **Keyboard Support**: Arrow keys and escape key navigation
- **Touch Gestures**: Mobile-friendly swipe navigation

### 📱 **QR Code Sharing**
- **Unique URLs**: Each love story gets a unique shareable link
- **QR Code Generation**: High-quality QR codes for easy mobile sharing
- **Download Option**: Save QR codes as PNG images
- **Native Sharing**: Uses device's native sharing capabilities when available

### 💾 **Data Management**
- **Local Storage**: Saves created pages locally for 30 days
- **File Handling**: Efficient image compression and preview generation
- **Data Validation**: Comprehensive form and file validation
- **Error Handling**: Graceful error handling with user-friendly messages

## 🚀 **Technical Implementation**

### **Frontend Stack**
- **React 18**: Modern React with hooks and functional components
- **Vite**: Fast development server and build tool
- **React Router**: Client-side routing for navigation
- **CSS Variables**: Consistent theming and easy customization

### **Key Libraries**
- **QRCode**: QR code generation for sharing
- **UUID**: Unique identifier generation
- **React Router DOM**: Navigation and routing

### **Performance Optimizations**
- **CSS Transforms**: GPU-accelerated animations
- **Image Compression**: Automatic image optimization
- **Lazy Loading**: Efficient resource loading
- **Reduced Motion**: Respects user accessibility preferences

## 🛠️ **Installation & Setup**

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## 📱 **Usage**

1. **Create Your Story**: Fill in both names, write a heartfelt message
2. **Add Memories**: Upload your favorite photos together
3. **Set the Mood**: Add background music for the perfect atmosphere
4. **Share with Love**: Generate a QR code or share the unique link
5. **Experience Together**: Your partner can scan the QR code to view your creation

## 🎯 **User Experience Flow**

1. **Landing Page**: Beautiful form with galaxy background
2. **Form Validation**: Real-time feedback and error handling
3. **Smooth Transition**: Animated transition to results page
4. **Romantic Display**: Names fall from top, message appears with typewriter effect
5. **Image Gallery**: Photos appear with staggered animations
6. **Audio Experience**: Background music enhances the romantic atmosphere
7. **Sharing Options**: QR code generation and multiple sharing methods

## 🔧 **File Structure**

```
src/
├── components/
│   ├── StarryBackground/     # Animated galaxy background
│   ├── LandingPage/          # Main form interface
│   ├── RomanticDisplay/      # Results page with animations
│   ├── ImageGallery/         # Lightbox image gallery
│   ├── AudioPlayer/          # Music controls
│   └── QRGenerator/          # QR code sharing modal
├── styles/
│   └── animations.css        # All keyframe animations
├── utils/
│   ├── storage.js           # Local storage management
│   ├── fileHandling.js      # File upload utilities
│   └── validation.js        # Form validation
└── App.jsx                  # Main application component
```

## 🌟 **Key Features Highlights**

- **60fps Animations**: Smooth, professional animations optimized for performance
- **Accessibility**: ARIA labels, keyboard navigation, and reduced motion support
- **Cross-browser Compatibility**: Works on all modern browsers
- **Mobile Responsive**: Perfect experience on all device sizes
- **File Validation**: Comprehensive validation for images and audio files
- **Error Handling**: Graceful error handling with user-friendly messages
- **Local Storage**: Automatic saving with 30-day retention
- **Unique URLs**: Each love story gets a permanent shareable link

## 💝 **Perfect For**

- Anniversaries and special occasions
- Valentine's Day surprises
- Proposal preparations
- Long-distance relationships
- Wedding memories
- Birthday surprises for your partner

---

*Created with 💖 for couples who want to share their love in a beautiful, memorable way.*
