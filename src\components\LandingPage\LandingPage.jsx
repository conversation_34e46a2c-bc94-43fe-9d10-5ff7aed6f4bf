import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { v4 as uuidv4 } from 'uuid';
import { validateForm, getCharacterCount } from '../../utils/validation';
import { validateImageFile, validateAudioFile, fileToBase64, createImagePreview, createAudioPreview, formatFileSize } from '../../utils/fileHandling';
import { saveRomanticPage } from '../../utils/storage';
import { getRandomLoveQuote, getRandomLoveMessageTemplate, getRandomRomanticEmojis } from '../../utils/loveQuotes';
import ProgressIndicator from '../ProgressIndicator/ProgressIndicator';
import ThemeSelector from '../ThemeSelector/ThemeSelector';
import StorageIndicator from '../StorageIndicator/StorageIndicator';
import MusicSelector from '../MusicSelector/MusicSelector';
import './LandingPage.css';

const LandingPage = () => {
  const navigate = useNavigate();
  const imageInputRef = useRef(null);
  const audioInputRef = useRef(null);

  const [formData, setFormData] = useState({
    myName: '',
    partnerName: '',
    loveMessage: '',
    images: [],
    audio: null
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const [currentQuote, setCurrentQuote] = useState(null);
  const [showInspiration, setShowInspiration] = useState(false);
  const [isGeneratingTemplate, setIsGeneratingTemplate] = useState(false);
  const [submissionStep, setSubmissionStep] = useState(0);
  const [currentTheme, setCurrentTheme] = useState('galaxy');
  const [showThemeSelector, setShowThemeSelector] = useState(false);

  const maxMessageLength = 1000;
  const messageCharCount = getCharacterCount(formData.loveMessage, maxMessageLength);

  // Load initial quote on component mount
  useEffect(() => {
    setCurrentQuote(getRandomLoveQuote());
  }, []);

  // Scroll reveal animation
  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('revealed');
        }
      });
    }, observerOptions);

    // Observe all reveal elements
    const revealElements = document.querySelectorAll('.reveal-up, .reveal-down, .reveal-left, .reveal-right');
    revealElements.forEach(el => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  const generateLoveMessageTemplate = () => {
    if (!formData.partnerName.trim()) {
      setErrors(prev => ({
        ...prev,
        partnerName: 'Please enter your partner\'s name first to generate a personalized message'
      }));
      return;
    }

    setIsGeneratingTemplate(true);

    // Simulate generation delay for better UX
    setTimeout(() => {
      const template = getRandomLoveMessageTemplate(formData.partnerName.trim());
      setFormData(prev => ({
        ...prev,
        loveMessage: template
      }));
      setIsGeneratingTemplate(false);

      // Clear any existing errors
      setErrors(prev => ({
        ...prev,
        loveMessage: '',
        partnerName: ''
      }));
    }, 1000);
  };

  const refreshQuote = () => {
    setCurrentQuote(getRandomLoveQuote());
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleImageUpload = async (files) => {
    const newImages = [];
    const fileArray = Array.from(files);

    for (const file of fileArray) {
      const validation = validateImageFile(file);
      if (!validation.valid) {
        setErrors(prev => ({
          ...prev,
          images: validation.error
        }));
        continue;
      }

      try {
        const preview = await createImagePreview(file);
        const base64 = await fileToBase64(file);
        
        newImages.push({
          id: uuidv4(),
          file,
          base64,
          preview: preview.thumbnail,
          name: file.name,
          size: file.size
        });
      } catch (error) {
        console.error('Error processing image:', error);
      }
    }

    if (newImages.length > 0) {
      setFormData(prev => ({
        ...prev,
        images: [...prev.images, ...newImages].slice(0, 10) // Max 10 images
      }));
      setErrors(prev => ({
        ...prev,
        images: ''
      }));
    }
  };

  const handleAudioUpload = async (file) => {
    const validation = validateAudioFile(file);
    if (!validation.valid) {
      setErrors(prev => ({
        ...prev,
        audio: validation.error
      }));
      return;
    }

    try {
      const preview = await createAudioPreview(file);
      const base64 = await fileToBase64(file);
      
      setFormData(prev => ({
        ...prev,
        audio: {
          file,
          base64,
          name: file.name,
          size: file.size,
          duration: preview.duration
        }
      }));
      setErrors(prev => ({
        ...prev,
        audio: ''
      }));
    } catch (error) {
      console.error('Error processing audio:', error);
      setErrors(prev => ({
        ...prev,
        audio: 'Failed to process audio file'
      }));
    }
  };

  const removeImage = (imageId) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter(img => img.id !== imageId)
    }));
  };

  const removeAudio = () => {
    setFormData(prev => ({
      ...prev,
      audio: null
    }));
  };

  const handleMusicSelect = (musicData) => {
    setFormData(prev => ({ ...prev, audio: musicData }));
    setErrors(prev => ({ ...prev, audio: null }));
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));
    
    if (imageFiles.length > 0) {
      handleImageUpload(imageFiles);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmissionStep(1);

    const validation = validateForm(formData);
    if (!validation.valid) {
      setErrors(validation.errors);
      setIsSubmitting(false);
      setSubmissionStep(0);
      return;
    }

    try {
      // Step 1: Validating data
      await new Promise(resolve => setTimeout(resolve, 800));
      setSubmissionStep(2);

      // Step 2: Processing images
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSubmissionStep(3);

      // Step 3: Setting up audio
      await new Promise(resolve => setTimeout(resolve, 600));
      setSubmissionStep(4);

      // Step 4: Creating love story
      const pageId = uuidv4();
      await new Promise(resolve => setTimeout(resolve, 800));
      setSubmissionStep(5);

      // Step 5: Finalizing
      const result = await saveRomanticPage(pageId, formData);
      await new Promise(resolve => setTimeout(resolve, 500));

      if (result.success) {
        // Show success message if optimized or emergency save
        if (result.emergency) {
          setErrors({
            submit: `✅ ${result.message || 'Love story saved with optimized compression!'}`,
            type: 'success'
          });
          await new Promise(resolve => setTimeout(resolve, 2000));
        } else if (result.optimized) {
          setErrors({
            submit: '✅ Love story saved! Images were optimized for better performance.',
            type: 'success'
          });
          await new Promise(resolve => setTimeout(resolve, 1500));
        }
        navigate(`/story/${pageId}`);
      } else {
        setErrors({
          submit: result.error || 'Failed to save your romantic page. Please try again.',
          type: 'error'
        });
        setSubmissionStep(0);
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      setErrors({ submit: 'An unexpected error occurred. Please try again.' });
      setSubmissionStep(0);
    } finally {
      setIsSubmitting(false);
    }
  };

  const progressSteps = [
    'Validate Details',
    'Process Images',
    'Setup Audio',
    'Create Story',
    'Finalize'
  ];

  return (
    <div className="landing-page">
      <div className="container">
        {/* Theme Selector */}
        <ThemeSelector
          currentTheme={currentTheme}
          onThemeChange={setCurrentTheme}
          isOpen={showThemeSelector}
          onToggle={() => setShowThemeSelector(!showThemeSelector)}
        />

        {/* Storage Indicator */}
        <StorageIndicator />

        {/* Progress Indicator Overlay */}
        {isSubmitting && (
          <div className="progress-overlay">
            <ProgressIndicator
              currentStep={submissionStep}
              totalSteps={5}
              steps={progressSteps}
            />
          </div>
        )}
        {/* Enhanced Hero Section */}
        <header className="landing-header">
          <div className="hero-content">
            <div className="hero-badge reveal-up stagger-1">
              <span className="badge badge-primary">✨ Create Magic Together</span>
            </div>

            <h1 className="hero-title reveal-up stagger-2 text-gradient">
              Create Your Love Story
            </h1>

            <p className="hero-subtitle reveal-up stagger-3">
              Transform your feelings into a beautiful, personalized romantic experience
              that captures your unique love story forever
            </p>

            <div className="hero-features reveal-up stagger-4">
              <div className="feature-item">
                <div className="feature-icon">💝</div>
                <span>Personalized Messages</span>
              </div>
              <div className="feature-item">
                <div className="feature-icon">📸</div>
                <span>Beautiful Galleries</span>
              </div>
              <div className="feature-item">
                <div className="feature-icon">🎵</div>
                <span>Audio Memories</span>
              </div>
              <div className="feature-item">
                <div className="feature-icon">✨</div>
                <span>Magical Animations</span>
              </div>
            </div>
          </div>

          {/* Love Quote Inspiration */}
          {currentQuote && (
            <div className="inspiration-section animate-fade-in-up animate-delay-500">
              <button
                className="inspiration-toggle"
                onClick={() => setShowInspiration(!showInspiration)}
                aria-label="Toggle inspiration"
              >
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M9 18l6-6-6-6" />
                </svg>
                Need some inspiration?
              </button>

              {showInspiration && (
                <div className="inspiration-content animate-fade-in-up">
                  <div className="love-quote">
                    <blockquote>
                      "{currentQuote.text}"
                    </blockquote>
                    <cite>— {currentQuote.author}</cite>
                  </div>
                  <div className="inspiration-actions">
                    <button
                      onClick={refreshQuote}
                      className="refresh-quote-btn"
                      aria-label="Get new quote"
                    >
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <polyline points="23,4 23,10 17,10" />
                        <polyline points="1,20 1,14 7,14" />
                        <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" />
                      </svg>
                      New Quote
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </header>

        <form onSubmit={handleSubmit} className="romantic-form card reveal-up stagger-5">
          <div className="form-header">
            <h2>Tell Your Love Story</h2>
            <p>Fill in the details to create your personalized romantic experience</p>
          </div>

          {/* Name Fields */}
          <div className="form-section reveal-up stagger-6">
            <h3>Who's This Love Story About?</h3>
            <div className="form-row">
              <div className="input-group">
                <input
                  type="text"
                  id="myName"
                  value={formData.myName}
                  onChange={(e) => handleInputChange('myName', e.target.value)}
                  placeholder=" "
                  className={`input input-floating ${errors.myName ? 'error' : ''}`}
                  aria-describedby={errors.myName ? 'myName-error' : undefined}
                />
                <label htmlFor="myName" className="label-floating">Your Name</label>
                {errors.myName && (
                  <span id="myName-error" className="error-message" role="alert">
                    {errors.myName}
                  </span>
                )}
              </div>

              <div className="input-group">
                <input
                  type="text"
                  id="partnerName"
                  value={formData.partnerName}
                  onChange={(e) => handleInputChange('partnerName', e.target.value)}
                  placeholder=" "
                  className={`input input-floating ${errors.partnerName ? 'error' : ''}`}
                  aria-describedby={errors.partnerName ? 'partnerName-error' : undefined}
                />
                <label htmlFor="partnerName" className="label-floating">Your Partner's Name</label>
                {errors.partnerName && (
                  <span id="partnerName-error" className="error-message" role="alert">
                    {errors.partnerName}
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Love Message */}
          <div className="form-section reveal-up stagger-7">
            <h3>Share Your Heart</h3>
            <div className="message-section">
              <div className="message-header">
                <button
                  type="button"
                  onClick={generateLoveMessageTemplate}
                  disabled={isGeneratingTemplate}
                  className="btn btn-outline btn-sm hover-glow"
                  title="Generate a personalized love message template"
                >
                  {isGeneratingTemplate ? (
                    <>
                      <div className="spinner"></div>
                      Generating...
                    </>
                  ) : (
                    <>
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" width="16" height="16">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                      </svg>
                      Generate Template
                    </>
                  )}
                </button>
              </div>

              <div className="input-group">
                <textarea
                  id="loveMessage"
                  value={formData.loveMessage}
                  onChange={(e) => handleInputChange('loveMessage', e.target.value)}
                  placeholder=" "
                  rows={6}
                  maxLength={maxMessageLength}
                  className={`input input-floating ${errors.loveMessage ? 'error' : ''}`}
                  aria-describedby={errors.loveMessage ? 'loveMessage-error' : 'loveMessage-count'}
                />
                <label htmlFor="loveMessage" className="label-floating">Your Love Message</label>

                <div className="message-footer">
                  <div className="character-count">
                    <span
                      id="loveMessage-count"
                      className={messageCharCount.isOverLimit ? 'text-error' : 'text-muted'}
                    >
                      {messageCharCount.current}/{messageCharCount.max} characters
                    </span>
                  </div>
                  <div className="message-tips">
                    💡 Tip: Be specific about your favorite memories together
                  </div>
                </div>

                {errors.loveMessage && (
                  <span id="loveMessage-error" className="error-message" role="alert">
                    {errors.loveMessage}
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Image Upload */}
          <div className="form-group">
            <label>Upload Images (JPG, PNG, GIF)</label>
            <div 
              className={`file-drop-zone ${dragOver ? 'drag-over' : ''}`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => imageInputRef.current?.click()}
            >
              <div className="drop-zone-content">
                <svg className="upload-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                  <polyline points="7,10 12,15 17,10" />
                  <line x1="12" y1="15" x2="12" y2="3" />
                </svg>
                <p>Click to upload or drag and drop images here</p>
                <p className="file-info">Maximum 10 images, 5MB each</p>
                <p className="file-tip">💡 Images are automatically optimized for storage</p>
              </div>
            </div>
            <input
              ref={imageInputRef}
              type="file"
              accept="image/jpeg,image/jpg,image/png,image/gif"
              multiple
              onChange={(e) => handleImageUpload(e.target.files)}
              className="hidden"
              aria-label="Upload images"
            />
            {errors.images && (
              <span className="error-message" role="alert">
                {errors.images}
              </span>
            )}
          </div>

          {/* Image Previews */}
          {formData.images.length > 0 && (
            <div className="image-previews">
              {formData.images.map((image, index) => (
                <div key={image.id} className={`image-preview animate-fade-in-rotate animate-delay-${(index + 1) * 100}`}>
                  <img src={image.preview} alt={`Preview ${index + 1}`} />
                  <div className="image-info">
                    <span className="image-name">{image.name}</span>
                    <span className="image-size">{formatFileSize(image.size)}</span>
                  </div>
                  <button
                    type="button"
                    onClick={() => removeImage(image.id)}
                    className="remove-button"
                    aria-label={`Remove ${image.name}`}
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          )}

          {/* Enhanced Audio Section */}
          <div className="form-section reveal-up stagger-9">
            <h3>Choose Background Music</h3>
            <p className="section-description">
              Select from our romantic music library or upload your own special song
            </p>

            <MusicSelector
              onSelectMusic={handleMusicSelect}
              selectedMusic={formData.audio}
              onRemoveMusic={removeAudio}
            />

            {/* Custom Upload Option */}
            <div className="custom-upload-section">
              <div className="divider">
                <span>Or upload your own</span>
              </div>

              <div className="audio-upload">
                <button
                  type="button"
                  onClick={() => audioInputRef.current?.click()}
                  className="btn btn-outline"
                >
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" width="16" height="16">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                    <polyline points="7,10 12,15 17,10" />
                    <line x1="12" y1="15" x2="12" y2="3" />
                  </svg>
                  Upload Custom Audio
                </button>
                <span className="file-info">MP3 or WAV, maximum 10MB</span>
              </div>

              <input
                ref={audioInputRef}
                type="file"
                accept="audio/mpeg,audio/mp3,audio/wav"
                onChange={(e) => e.target.files[0] && handleAudioUpload(e.target.files[0])}
                className="hidden"
                aria-label="Upload background music"
              />
            </div>

            {errors.audio && (
              <span className="error-message" role="alert">
                {errors.audio}
              </span>
            )}
          </div>

          {/* Submit Button */}
          <div className="form-actions reveal-up stagger-10">
            <button
              type="submit"
              disabled={isSubmitting}
              className="btn btn-primary btn-lg w-full hover-lift"
            >
              {isSubmitting ? (
                <>
                  <div className="spinner"></div>
                  Creating Your Love Story...
                </>
              ) : (
                <>
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" width="20" height="20">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                    <polyline points="17,8 12,3 7,8" />
                    <line x1="12" y1="3" x2="12" y2="15" />
                  </svg>
                  Create Love Story
                </>
              )}
            </button>

            {errors.submit && (
              <div className={`toast toast-${errors.type || 'error'}`} role="alert">
                {errors.submit}
              </div>
            )}

            <p className="form-disclaimer text-muted text-center">
              Your love story will be saved securely and can be shared with a unique link
            </p>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LandingPage;
