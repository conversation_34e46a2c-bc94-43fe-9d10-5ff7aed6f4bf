// Form validation utilities

// Validate name field
export const validateName = (name) => {
  const trimmedName = name.trim();
  
  if (!trimmedName) {
    return { valid: false, error: 'Name is required' };
  }
  
  if (trimmedName.length < 2) {
    return { valid: false, error: 'Name must be at least 2 characters long' };
  }
  
  if (trimmedName.length > 50) {
    return { valid: false, error: 'Name must be less than 50 characters' };
  }
  
  // Check for valid characters (letters, spaces, hyphens, apostrophes, and international characters)
  // Updated regex to support Vietnamese, Chinese, Japanese, Korean, Arabic, and other international characters
  const nameRegex = /^[\p{L}\p{M}\s\-'\.]+$/u;
  if (!nameRegex.test(trimmedName)) {
    return { valid: false, error: 'Name can only contain letters, spaces, hyphens, apostrophes, and accented characters' };
  }
  
  return { valid: true, value: trimmedName };
};

// Validate love message
export const validateLoveMessage = (message) => {
  const trimmedMessage = message.trim();
  
  if (!trimmedMessage) {
    return { valid: false, error: 'Love message is required' };
  }
  
  if (trimmedMessage.length < 10) {
    return { valid: false, error: 'Love message must be at least 10 characters long' };
  }
  
  if (trimmedMessage.length > 1000) {
    return { valid: false, error: 'Love message must be less than 1000 characters' };
  }
  
  return { valid: true, value: trimmedMessage };
};

// Validate image files array
export const validateImages = (images) => {
  if (!images || images.length === 0) {
    return { valid: false, error: 'At least one image is required' };
  }
  
  if (images.length > 10) {
    return { valid: false, error: 'Maximum 10 images allowed' };
  }
  
  // Check each image
  for (let i = 0; i < images.length; i++) {
    const image = images[i];
    
    if (!image.file && !image.base64) {
      return { valid: false, error: `Image ${i + 1} is invalid` };
    }
    
    if (image.file) {
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
      if (!validTypes.includes(image.file.type)) {
        return { valid: false, error: `Image ${i + 1} must be JPG, PNG, or GIF format` };
      }
      
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (image.file.size > maxSize) {
        return { valid: false, error: `Image ${i + 1} must be less than 5MB` };
      }
    }
  }
  
  return { valid: true };
};

// Validate audio file
export const validateAudio = (audio) => {
  if (!audio) {
    return { valid: true }; // Audio is optional
  }
  
  if (!audio.file && !audio.base64) {
    return { valid: false, error: 'Audio file is invalid' };
  }
  
  if (audio.file) {
    const validTypes = ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/wave'];
    if (!validTypes.includes(audio.file.type)) {
      return { valid: false, error: 'Audio must be MP3 or WAV format' };
    }
    
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (audio.file.size > maxSize) {
      return { valid: false, error: 'Audio file must be less than 10MB' };
    }
  }
  
  return { valid: true };
};

// Validate entire form
export const validateForm = (formData) => {
  const errors = {};
  
  // Validate my name
  const myNameValidation = validateName(formData.myName);
  if (!myNameValidation.valid) {
    errors.myName = myNameValidation.error;
  }
  
  // Validate partner name
  const partnerNameValidation = validateName(formData.partnerName);
  if (!partnerNameValidation.valid) {
    errors.partnerName = partnerNameValidation.error;
  }
  
  // Validate love message
  const messageValidation = validateLoveMessage(formData.loveMessage);
  if (!messageValidation.valid) {
    errors.loveMessage = messageValidation.error;
  }
  
  // Validate images
  const imagesValidation = validateImages(formData.images);
  if (!imagesValidation.valid) {
    errors.images = imagesValidation.error;
  }
  
  // Validate audio (optional)
  const audioValidation = validateAudio(formData.audio);
  if (!audioValidation.valid) {
    errors.audio = audioValidation.error;
  }
  
  return {
    valid: Object.keys(errors).length === 0,
    errors
  };
};

// Sanitize text input
export const sanitizeText = (text) => {
  return text
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/\s+/g, ' '); // Replace multiple spaces with single space
};

// Check if string contains profanity (basic check)
export const containsProfanity = (text) => {
  // Basic profanity filter - in production, use a more comprehensive solution
  const profanityWords = [
    // Add basic profanity words here
    'badword1', 'badword2' // Placeholder - replace with actual words if needed
  ];
  
  const lowerText = text.toLowerCase();
  return profanityWords.some(word => lowerText.includes(word));
};

// Validate URL format
export const validateUrl = (url) => {
  try {
    new URL(url);
    return { valid: true };
  } catch {
    return { valid: false, error: 'Invalid URL format' };
  }
};

// Character count helper
export const getCharacterCount = (text, maxLength) => {
  const currentLength = text.length;
  const remaining = maxLength - currentLength;
  
  return {
    current: currentLength,
    max: maxLength,
    remaining,
    isOverLimit: remaining < 0,
    percentage: (currentLength / maxLength) * 100
  };
};
