/* Galaxy Animation Keyframes */

/* Starry Background Animations */
@keyframes twinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(5px) rotate(-1deg);
  }
}

@keyframes drift {
  0% {
    transform: translateX(0px);
  }
  100% {
    transform: translateX(-100px);
  }
}

/* Text Entrance Animations */
@keyframes fallFromTop {
  0% {
    opacity: 0;
    transform: translateY(-100px) scale(0.8);
  }
  50% {
    opacity: 0.7;
    transform: translateY(10px) scale(1.05);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typewriter {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}

@keyframes blinkCursor {
  0%, 50% {
    border-color: var(--galaxy-gold);
  }
  51%, 100% {
    border-color: transparent;
  }
}

/* Image Entrance Animations */
@keyframes fadeInRotate {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(-10deg);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1) rotate(5deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-100px) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes slideInFromRight {
  0% {
    opacity: 0;
    transform: translateX(100px) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* Particle Effects */
@keyframes particleFloat {
  0% {
    transform: translateY(100vh) translateX(0px) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(50px) scale(1);
    opacity: 0;
  }
}

@keyframes heartBeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Form Transition Animations */
@keyframes slideOut {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-100%);
  }
}

@keyframes slideIn {
  0% {
    opacity: 0;
    transform: translateX(100%);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes zoomIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Glow Effects */
@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--galaxy-gold);
  }
  50% {
    box-shadow: 0 0 20px var(--galaxy-gold), 0 0 30px var(--galaxy-gold);
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 25px rgba(255, 215, 0, 0.6), 0 0 35px rgba(255, 215, 0, 0.4);
  }
}

/* Enhanced Professional Animations */

/* Modal and Page Transitions */
@keyframes modalSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes toastSlideIn {
  0% {
    opacity: 0;
    transform: translateX(100%);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pageSlideIn {
  0% {
    opacity: 0;
    transform: translateX(100%);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pageSlideOut {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-100%);
  }
}

/* Loading and Progress Animations */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes progressShine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Scroll-triggered Animations */
@keyframes revealUp {
  0% {
    opacity: 0;
    transform: translateY(60px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes revealDown {
  0% {
    opacity: 0;
    transform: translateY(-60px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes revealLeft {
  0% {
    opacity: 0;
    transform: translateX(-60px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes revealRight {
  0% {
    opacity: 0;
    transform: translateX(60px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Micro-interactions */
@keyframes buttonPress {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10px);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

/* Animation Classes */
.animate-twinkle {
  animation: twinkle 2s ease-in-out infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-drift {
  animation: drift 20s linear infinite;
}

.animate-fall-from-top {
  animation: fallFromTop 1s ease-out forwards;
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-typewriter {
  overflow: hidden;
  border-right: 2px solid var(--galaxy-gold);
  white-space: nowrap;
  animation: typewriter 3s steps(40) forwards, blinkCursor 1s step-end infinite;
}

.animate-fade-in-rotate {
  animation: fadeInRotate 1s ease-out forwards;
}

.animate-slide-in-left {
  animation: slideInFromLeft 0.8s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.8s ease-out forwards;
}

.animate-particle-float {
  animation: particleFloat 8s linear infinite;
}

.animate-heart-beat {
  animation: heartBeat 1.5s ease-in-out infinite;
}

.animate-slide-out {
  animation: slideOut 0.5s ease-in forwards;
}

.animate-slide-in {
  animation: slideIn 0.5s ease-out forwards;
}

.animate-zoom-in {
  animation: zoomIn 0.6s ease-out forwards;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

/* Staggered Animation Delays */
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-400 { animation-delay: 0.4s; }
.animate-delay-500 { animation-delay: 0.5s; }
.animate-delay-600 { animation-delay: 0.6s; }
.animate-delay-700 { animation-delay: 0.7s; }
.animate-delay-800 { animation-delay: 0.8s; }
.animate-delay-900 { animation-delay: 0.9s; }
.animate-delay-1000 { animation-delay: 1s; }

/* Performance Optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Responsive Animation Controls */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Additional missing keyframes */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes blinkCursor {
  0%, 50% { border-color: var(--galaxy-gold); }
  51%, 100% { border-color: transparent; }
}

@media (max-width: 768px) {
  .animate-fall-from-top,
  .animate-fade-in-up,
  .animate-slide-in-left,
  .animate-slide-in-right {
    animation-duration: 0.6s;
  }
}

/* Enhanced Animation Classes */
.animate-fade-in {
  animation: fadeInUp 0.6s var(--ease-out);
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s var(--ease-out);
}

.animate-fade-in-down {
  animation: revealDown 0.6s var(--ease-out);
}

.animate-fade-in-left {
  animation: revealLeft 0.6s var(--ease-out);
}

.animate-fade-in-right {
  animation: revealRight 0.6s var(--ease-out);
}

.animate-scale-in {
  animation: zoomIn 0.4s var(--ease-bounce);
}

.animate-slide-in {
  animation: slideIn 0.5s var(--ease-out);
}

.animate-slide-out {
  animation: slideOut 0.5s var(--ease-in);
}

.animate-bounce {
  animation: bounce 1s var(--ease-bounce);
}

.animate-shake {
  animation: shake 0.5s var(--ease-in-out);
}

.animate-pulse {
  animation: heartBeat 2s ease-in-out infinite;
}

.animate-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-button-press {
  animation: buttonPress 0.15s var(--ease-out);
}

/* Page Transition Classes */
.page-enter {
  animation: pageSlideIn 0.5s var(--ease-out);
}

.page-exit {
  animation: pageSlideOut 0.5s var(--ease-in);
}

.modal-enter {
  animation: modalSlideIn 0.3s var(--ease-back);
}

.toast-enter {
  animation: toastSlideIn 0.3s var(--ease-out);
}

/* Scroll Reveal Classes */
.reveal-up {
  opacity: 0;
  transform: translateY(60px);
  transition: all 0.6s var(--ease-out);
}

.reveal-up.revealed {
  opacity: 1;
  transform: translateY(0);
}

.reveal-down {
  opacity: 0;
  transform: translateY(-60px);
  transition: all 0.6s var(--ease-out);
}

.reveal-down.revealed {
  opacity: 1;
  transform: translateY(0);
}

.reveal-left {
  opacity: 0;
  transform: translateX(-60px);
  transition: all 0.6s var(--ease-out);
}

.reveal-left.revealed {
  opacity: 1;
  transform: translateX(0);
}

.reveal-right {
  opacity: 0;
  transform: translateX(60px);
  transition: all 0.6s var(--ease-out);
}

.reveal-right.revealed {
  opacity: 1;
  transform: translateX(0);
}

/* Stagger Animation Delays */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }
.stagger-6 { animation-delay: 0.6s; }

/* Hover Animations */
.hover-lift {
  transition: transform 0.2s var(--ease-out);
}

.hover-lift:hover {
  transform: translateY(-4px);
}

.hover-scale {
  transition: transform 0.2s var(--ease-out);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow 0.3s var(--ease-out);
}

.hover-glow:hover {
  box-shadow: var(--shadow-glow-lg);
}
