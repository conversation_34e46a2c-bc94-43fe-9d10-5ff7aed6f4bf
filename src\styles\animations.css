/* Galaxy Animation Keyframes */

/* Starry Background Animations */
@keyframes twinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(5px) rotate(-1deg);
  }
}

@keyframes drift {
  0% {
    transform: translateX(0px);
  }
  100% {
    transform: translateX(-100px);
  }
}

/* Text Entrance Animations */
@keyframes fallFromTop {
  0% {
    opacity: 0;
    transform: translateY(-100px) scale(0.8);
  }
  50% {
    opacity: 0.7;
    transform: translateY(10px) scale(1.05);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typewriter {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}

@keyframes blinkCursor {
  0%, 50% {
    border-color: var(--galaxy-gold);
  }
  51%, 100% {
    border-color: transparent;
  }
}

/* Image Entrance Animations */
@keyframes fadeInRotate {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(-10deg);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1) rotate(5deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-100px) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes slideInFromRight {
  0% {
    opacity: 0;
    transform: translateX(100px) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* Particle Effects */
@keyframes particleFloat {
  0% {
    transform: translateY(100vh) translateX(0px) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(50px) scale(1);
    opacity: 0;
  }
}

@keyframes heartBeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Form Transition Animations */
@keyframes slideOut {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-100%);
  }
}

@keyframes slideIn {
  0% {
    opacity: 0;
    transform: translateX(100%);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes zoomIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Glow Effects */
@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--galaxy-gold);
  }
  50% {
    box-shadow: 0 0 20px var(--galaxy-gold), 0 0 30px var(--galaxy-gold);
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 25px rgba(255, 215, 0, 0.6), 0 0 35px rgba(255, 215, 0, 0.4);
  }
}

/* Animation Classes */
.animate-twinkle {
  animation: twinkle 2s ease-in-out infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-drift {
  animation: drift 20s linear infinite;
}

.animate-fall-from-top {
  animation: fallFromTop 1s ease-out forwards;
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-typewriter {
  overflow: hidden;
  border-right: 2px solid var(--galaxy-gold);
  white-space: nowrap;
  animation: typewriter 3s steps(40) forwards, blinkCursor 1s step-end infinite;
}

.animate-fade-in-rotate {
  animation: fadeInRotate 1s ease-out forwards;
}

.animate-slide-in-left {
  animation: slideInFromLeft 0.8s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.8s ease-out forwards;
}

.animate-particle-float {
  animation: particleFloat 8s linear infinite;
}

.animate-heart-beat {
  animation: heartBeat 1.5s ease-in-out infinite;
}

.animate-slide-out {
  animation: slideOut 0.5s ease-in forwards;
}

.animate-slide-in {
  animation: slideIn 0.5s ease-out forwards;
}

.animate-zoom-in {
  animation: zoomIn 0.6s ease-out forwards;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

/* Staggered Animation Delays */
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-400 { animation-delay: 0.4s; }
.animate-delay-500 { animation-delay: 0.5s; }
.animate-delay-600 { animation-delay: 0.6s; }
.animate-delay-700 { animation-delay: 0.7s; }
.animate-delay-800 { animation-delay: 0.8s; }
.animate-delay-900 { animation-delay: 0.9s; }
.animate-delay-1000 { animation-delay: 1s; }

/* Performance Optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Responsive Animation Controls */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Additional missing keyframes */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes blinkCursor {
  0%, 50% { border-color: var(--galaxy-gold); }
  51%, 100% { border-color: transparent; }
}

@media (max-width: 768px) {
  .animate-fall-from-top,
  .animate-fade-in-up,
  .animate-slide-in-left,
  .animate-slide-in-right {
    animation-duration: 0.6s;
  }
}
