/* Navigation Styles */
.navigation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-sticky);
  background: rgba(10, 10, 26, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all var(--transition-normal);
}

.navigation.scrolled {
  background: rgba(10, 10, 26, 0.95);
  box-shadow: var(--shadow-lg);
  border-bottom-color: rgba(255, 215, 0, 0.2);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

/* Logo */
.nav-logo {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  text-decoration: none;
  transition: all var(--transition-normal);
}

.nav-logo:hover {
  transform: scale(1.05);
}

.logo-icon {
  font-size: var(--font-size-2xl);
  animation: heartPulse 2s ease-in-out infinite;
}

.logo-text {
  font-family: 'Dancing Script', cursive;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(135deg, var(--galaxy-gold), var(--galaxy-pink));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Desktop Navigation */
.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--space-6);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  text-decoration: none;
  color: var(--text-secondary);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
  transition: left 0.5s ease;
}

.nav-link:hover::before {
  left: 100%;
}

.nav-link:hover {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.nav-link.active {
  color: var(--galaxy-gold);
  background: rgba(255, 215, 0, 0.1);
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.nav-icon {
  font-size: var(--font-size-lg);
}

.nav-label {
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

/* Right Actions */
.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

/* Mobile Menu Button */
.mobile-menu-btn {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: var(--z-modal);
}

.mobile-menu-btn span {
  width: 100%;
  height: 2px;
  background: var(--text-primary);
  border-radius: 2px;
  transition: all 0.3s ease;
  transform-origin: center;
}

.mobile-menu-btn.open span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-btn.open span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-btn.open span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Menu */
.mobile-menu {
  position: fixed;
  top: 70px;
  left: 0;
  right: 0;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s var(--ease-out);
  z-index: var(--z-dropdown);
}

.mobile-menu.open {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.mobile-menu-content {
  padding: var(--space-4);
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
  text-decoration: none;
  color: var(--text-secondary);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-2);
  transition: all var(--transition-normal);
  animation: slideInLeft 0.3s var(--ease-out) forwards;
  opacity: 0;
  transform: translateX(-20px);
}

.mobile-menu.open .mobile-nav-link {
  opacity: 1;
  transform: translateX(0);
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
  color: var(--galaxy-gold);
  background: rgba(255, 215, 0, 0.1);
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.mobile-menu-footer {
  margin-top: var(--space-6);
  padding-top: var(--space-4);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.love-quote {
  font-style: italic;
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal-backdrop);
  animation: fadeIn 0.3s ease;
}

/* Animations */
@keyframes heartPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Responsive */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }
  
  .mobile-menu-btn {
    display: flex;
  }
  
  .nav-container {
    height: 60px;
  }
  
  .mobile-menu {
    top: 60px;
  }
  
  .logo-text {
    font-size: var(--font-size-lg);
  }
  
  .logo-icon {
    font-size: var(--font-size-xl);
  }
}

@media (max-width: 480px) {
  .nav-container {
    padding: 0 var(--space-3);
  }
  
  .nav-actions {
    gap: var(--space-2);
  }
}
