import React, { useState, useEffect } from 'react';
import './StorageIndicator.css';

const StorageIndicator = () => {
  const [storageInfo, setStorageInfo] = useState({
    used: 0,
    total: 5 * 1024 * 1024, // 5MB typical localStorage limit
    percentage: 0,
    romanticPages: 0
  });
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    updateStorageInfo();
  }, []);

  const updateStorageInfo = () => {
    try {
      let totalUsed = 0;
      let romanticPagesSize = 0;
      let romanticPagesCount = 0;

      // Calculate total localStorage usage
      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          const itemSize = localStorage[key].length + key.length;
          totalUsed += itemSize;
          
          if (key === 'romantic_pages') {
            romanticPagesSize = itemSize;
            try {
              const pages = JSON.parse(localStorage[key]);
              romanticPagesCount = Object.keys(pages).length;
            } catch (e) {
              // Ignore parsing errors
            }
          }
        }
      }

      const percentage = (totalUsed / storageInfo.total) * 100;

      setStorageInfo({
        used: totalUsed,
        total: storageInfo.total,
        percentage: Math.min(percentage, 100),
        romanticPages: romanticPagesCount,
        romanticPagesSize
      });
    } catch (error) {
      console.error('Error calculating storage usage:', error);
    }
  };

  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const getStorageStatus = () => {
    if (storageInfo.percentage < 50) return 'good';
    if (storageInfo.percentage < 80) return 'warning';
    return 'critical';
  };

  const clearOldPages = () => {
    if (window.confirm('This will remove old love stories to free up space. Continue?')) {
      try {
        // Keep only the most recent 3 pages
        const pages = JSON.parse(localStorage.getItem('romantic_pages') || '{}');
        const pageEntries = Object.entries(pages);
        
        // Sort by creation date (newest first)
        pageEntries.sort((a, b) => new Date(b[1].createdAt) - new Date(a[1].createdAt));
        
        // Keep only the 3 most recent
        const recentPages = {};
        pageEntries.slice(0, 3).forEach(([id, data]) => {
          recentPages[id] = data;
        });
        
        localStorage.setItem('romantic_pages', JSON.stringify(recentPages));
        updateStorageInfo();
        
        alert(`Cleaned up storage! Kept ${Object.keys(recentPages).length} most recent love stories.`);
      } catch (error) {
        console.error('Error cleaning storage:', error);
        alert('Error cleaning storage. Please try again.');
      }
    }
  };

  const status = getStorageStatus();

  return (
    <div className={`storage-indicator ${isExpanded ? 'expanded' : ''}`}>
      <button
        className={`storage-toggle ${status}`}
        onClick={() => setIsExpanded(!isExpanded)}
        title="Storage usage"
      >
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" />
          <polyline points="3.27,6.96 12,12.01 20.73,6.96" />
          <line x1="12" y1="22.08" x2="12" y2="12" />
        </svg>
        <span className="storage-percentage">{Math.round(storageInfo.percentage)}%</span>
      </button>

      {isExpanded && (
        <div className="storage-panel">
          <div className="storage-header">
            <h4>Storage Usage</h4>
            <button
              className="close-storage"
              onClick={() => setIsExpanded(false)}
              aria-label="Close storage panel"
            >
              ×
            </button>
          </div>

          <div className="storage-bar">
            <div 
              className={`storage-fill ${status}`}
              style={{ width: `${storageInfo.percentage}%` }}
            />
          </div>

          <div className="storage-details">
            <div className="storage-stat">
              <span className="stat-label">Used:</span>
              <span className="stat-value">{formatBytes(storageInfo.used)}</span>
            </div>
            <div className="storage-stat">
              <span className="stat-label">Available:</span>
              <span className="stat-value">{formatBytes(storageInfo.total - storageInfo.used)}</span>
            </div>
            <div className="storage-stat">
              <span className="stat-label">Love Stories:</span>
              <span className="stat-value">{storageInfo.romanticPages}</span>
            </div>
          </div>

          {status === 'critical' && (
            <div className="storage-warning">
              <p>⚠️ Storage is almost full!</p>
              <button onClick={clearOldPages} className="cleanup-button">
                Clean Up Old Stories
              </button>
            </div>
          )}

          {status === 'warning' && (
            <div className="storage-info">
              <p>💡 Consider cleaning up old stories if you encounter saving issues.</p>
            </div>
          )}

          <div className="storage-tips">
            <h5>💡 Storage Tips:</h5>
            <ul>
              <li>Images are automatically compressed</li>
              <li>Large audio files may be excluded</li>
              <li>Old stories are cleaned up automatically</li>
              <li>Each story is saved for 30 days</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default StorageIndicator;
