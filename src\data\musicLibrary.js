// Music Library with Pre-loaded Songs
export const musicLibrary = [
  {
    id: 'romantic-1',
    name: 'Perfect Love',
    artist: 'Romantic Collection',
    duration: 180, // 3 minutes
    genre: 'Romantic',
    mood: 'Sweet',
    description: 'A gentle, romantic melody perfect for love stories',
    // Using a placeholder audio URL - in real app, you'd have actual audio files
    url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    preview: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
  },
  {
    id: 'romantic-2',
    name: 'Eternal Promise',
    artist: 'Love Songs',
    duration: 210,
    genre: 'Romantic',
    mood: 'Emotional',
    description: 'Deep emotional ballad for meaningful moments',
    url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    preview: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
  },
  {
    id: 'romantic-3',
    name: 'Dancing Hearts',
    artist: 'Romantic Collection',
    duration: 195,
    genre: 'Romantic',
    mood: 'Joyful',
    description: 'Upbeat romantic tune for celebration of love',
    url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    preview: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
  },
  {
    id: 'romantic-4',
    name: 'Moonlight Serenade',
    artist: 'Classical Romance',
    duration: 240,
    genre: 'Classical',
    mood: 'Dreamy',
    description: 'Classical piano piece for intimate moments',
    url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    preview: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
  },
  {
    id: 'romantic-5',
    name: 'First Kiss',
    artist: 'Romantic Collection',
    duration: 165,
    genre: 'Romantic',
    mood: 'Tender',
    description: 'Soft, tender melody for special first moments',
    url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    preview: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
  },
  {
    id: 'romantic-6',
    name: 'Wedding Bells',
    artist: 'Celebration',
    duration: 220,
    genre: 'Romantic',
    mood: 'Celebratory',
    description: 'Joyful wedding celebration music',
    url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    preview: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
  },
  {
    id: 'romantic-7',
    name: 'Sunset Romance',
    artist: 'Ambient Love',
    duration: 300,
    genre: 'Ambient',
    mood: 'Peaceful',
    description: 'Peaceful ambient music for quiet romantic moments',
    url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    preview: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
  },
  {
    id: 'romantic-8',
    name: 'Love Letter',
    artist: 'Romantic Collection',
    duration: 175,
    genre: 'Romantic',
    mood: 'Nostalgic',
    description: 'Nostalgic melody perfect for reading love letters',
    url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    preview: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
  }
];

// Function to get random song
export const getRandomSong = () => {
  const randomIndex = Math.floor(Math.random() * musicLibrary.length);
  return musicLibrary[randomIndex];
};

// Function to get songs by mood
export const getSongsByMood = (mood) => {
  return musicLibrary.filter(song => song.mood.toLowerCase() === mood.toLowerCase());
};

// Function to get songs by genre
export const getSongsByGenre = (genre) => {
  return musicLibrary.filter(song => song.genre.toLowerCase() === genre.toLowerCase());
};

// Function to convert song to audio data format
export const convertSongToAudioData = async (song) => {
  try {
    // In a real app, you would fetch the actual audio file
    // For demo purposes, we'll create a mock audio data object
    return {
      name: song.name,
      size: 1024 * 1024 * 3, // 3MB mock size
      type: 'audio/mpeg',
      base64: song.url, // In real app, this would be base64 encoded audio
      duration: song.duration,
      artist: song.artist,
      genre: song.genre,
      mood: song.mood,
      description: song.description
    };
  } catch (error) {
    console.error('Error converting song:', error);
    return null;
  }
};

export default musicLibrary;
