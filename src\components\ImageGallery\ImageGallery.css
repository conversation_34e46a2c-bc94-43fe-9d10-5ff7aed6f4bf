.image-gallery {
  margin-bottom: var(--space-8);
}

.gallery-title {
  text-align: center;
  margin-bottom: var(--space-8);
  font-size: var(--font-size-2xl);
  color: var(--galaxy-gold);
  text-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
  max-width: 1000px;
  margin: 0 auto;
}

.gallery-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: var(--radius-lg);
  overflow: hidden;
  cursor: pointer;
  opacity: 0;
  transform: scale(0.5) rotate(-10deg);
  transition: all var(--transition-normal);
  border: 2px solid transparent;
  background: linear-gradient(var(--galaxy-dark-blue), var(--galaxy-dark-blue)) padding-box,
              linear-gradient(135deg, var(--galaxy-gold), var(--galaxy-pink)) border-box;
}

.gallery-item.animate-fade-in-rotate {
  animation: fadeInRotate 1s ease-out forwards;
}

.gallery-item:hover {
  transform: translateY(-8px) scale(1.02);
  border-color: var(--galaxy-gold);
  box-shadow:
    var(--shadow-glow),
    0 0 30px rgba(255, 215, 0, 0.4),
    inset 0 0 20px rgba(255, 215, 0, 0.1);
}

.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.gallery-item:hover .gallery-image {
  transform: scale(1.1);
}

.gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(45, 27, 105, 0.8) 50%,
    rgba(0, 0, 0, 0.7) 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.overlay-content {
  text-align: center;
  color: var(--galaxy-white);
  transform: translateY(20px);
  transition: transform var(--transition-normal);
}

.gallery-item:hover .overlay-content {
  transform: translateY(0);
}

.expand-icon {
  width: 32px;
  height: 32px;
  margin-bottom: var(--space-2);
  stroke-width: 2;
}

.overlay-text {
  font-size: var(--font-size-sm);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Lightbox Styles */
.lightbox-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.95);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
  backdrop-filter: blur(10px);
}

.lightbox-container {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.lightbox-close {
  position: absolute;
  top: -50px;
  right: 0;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid var(--galaxy-gold);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  color: var(--galaxy-white);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  z-index: 1001;
}

.lightbox-close:hover {
  background: var(--galaxy-gold);
  color: var(--galaxy-deep-blue);
  transform: scale(1.1);
}

.lightbox-close svg {
  width: 20px;
  height: 20px;
  stroke-width: 2;
}

.lightbox-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid var(--galaxy-gold);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  color: var(--galaxy-white);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  z-index: 1001;
}

.lightbox-nav:hover {
  background: var(--galaxy-gold);
  color: var(--galaxy-deep-blue);
  transform: translateY(-50%) scale(1.1);
}

.lightbox-nav svg {
  width: 24px;
  height: 24px;
  stroke-width: 2;
}

.lightbox-prev {
  left: -70px;
}

.lightbox-next {
  right: -70px;
}

.lightbox-image-container {
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 100%;
  max-height: 70vh;
}

.lightbox-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  animation: zoomIn 0.3s ease-out;
}

.lightbox-counter {
  position: absolute;
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%);
  color: var(--galaxy-white);
  font-size: var(--font-size-sm);
  font-weight: 600;
  background: rgba(0, 0, 0, 0.5);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-full);
  border: 1px solid var(--galaxy-gold);
}

.lightbox-thumbnails {
  position: absolute;
  bottom: -100px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: var(--space-2);
  max-width: 80vw;
  overflow-x: auto;
  padding: var(--space-2);
  scrollbar-width: thin;
  scrollbar-color: var(--galaxy-gold) transparent;
}

.lightbox-thumbnails::-webkit-scrollbar {
  height: 4px;
}

.lightbox-thumbnails::-webkit-scrollbar-track {
  background: transparent;
}

.lightbox-thumbnails::-webkit-scrollbar-thumb {
  background: var(--galaxy-gold);
  border-radius: 2px;
}

.thumbnail {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  border: 2px solid transparent;
  border-radius: var(--radius-md);
  overflow: hidden;
  cursor: pointer;
  transition: all var(--transition-fast);
  background: none;
  padding: 0;
}

.thumbnail:hover,
.thumbnail.active {
  border-color: var(--galaxy-gold);
  transform: scale(1.1);
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Responsive Design */
@media (max-width: 768px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
  }
  
  .gallery-title {
    font-size: var(--font-size-xl);
    margin-bottom: var(--space-6);
  }
  
  .lightbox-container {
    max-width: 95vw;
    max-height: 95vh;
  }
  
  .lightbox-close {
    top: -40px;
    right: -10px;
    width: 35px;
    height: 35px;
  }
  
  .lightbox-nav {
    width: 40px;
    height: 40px;
  }
  
  .lightbox-prev {
    left: -50px;
  }
  
  .lightbox-next {
    right: -50px;
  }
  
  .lightbox-image-container {
    max-height: 60vh;
  }
  
  .lightbox-counter {
    bottom: -30px;
    font-size: var(--font-size-xs);
  }
  
  .lightbox-thumbnails {
    bottom: -70px;
    max-width: 90vw;
  }
  
  .thumbnail {
    width: 50px;
    height: 50px;
  }
}

@media (max-width: 480px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-3);
  }
  
  .lightbox-nav {
    display: none; /* Hide navigation arrows on very small screens */
  }
  
  .lightbox-close {
    top: 10px;
    right: 10px;
    position: fixed;
  }
  
  .lightbox-counter {
    bottom: 10px;
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
  }
  
  .lightbox-thumbnails {
    display: none; /* Hide thumbnails on very small screens */
  }
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Accessibility */
.gallery-item:focus {
  outline: 2px solid var(--galaxy-gold);
  outline-offset: 2px;
}

.lightbox-close:focus,
.lightbox-nav:focus,
.thumbnail:focus {
  outline: 2px solid var(--galaxy-gold);
  outline-offset: 2px;
}

/* Performance optimizations */
.gallery-item,
.lightbox-image {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

@media (prefers-reduced-motion: reduce) {
  .gallery-item,
  .gallery-image,
  .gallery-overlay,
  .overlay-content,
  .lightbox-overlay,
  .lightbox-image {
    animation: none !important;
    transition: none !important;
  }
  
  .gallery-item.animate-fade-in-rotate {
    opacity: 1;
    transform: none;
  }
}
