:root {
  /* Enhanced Galaxy Theme Colors */
  --galaxy-deep-blue: #0a0a1a;
  --galaxy-dark-blue: #1a1a3a;
  --galaxy-medium-blue: #2a2a4a;
  --galaxy-purple: #2d1b69;
  --galaxy-light-purple: #4a3b8a;
  --galaxy-pink: #ff69b4;
  --galaxy-light-pink: #ff8cc8;
  --galaxy-gold: #ffd700;
  --galaxy-light-gold: #ffe55c;
  --galaxy-white: #ffffff;
  --galaxy-light-blue: #4a90e2;
  --galaxy-cyan: #00d4ff;
  --galaxy-lavender: #e6e6fa;
  
  /* Semantic Colors */
  --color-primary: var(--galaxy-gold);
  --color-secondary: var(--galaxy-pink);
  --color-accent: var(--galaxy-cyan);
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: var(--galaxy-light-blue);
  
  /* Background Colors */
  --bg-primary: var(--galaxy-deep-blue);
  --bg-secondary: var(--galaxy-dark-blue);
  --bg-tertiary: var(--galaxy-medium-blue);
  --bg-glass: rgba(26, 26, 58, 0.8);
  --bg-glass-light: rgba(255, 255, 255, 0.1);
  
  /* Text Colors */
  --text-primary: var(--galaxy-white);
  --text-secondary: var(--galaxy-lavender);
  --text-muted: rgba(255, 255, 255, 0.7);
  --text-accent: var(--galaxy-gold);
  
  /* Enhanced Spacing Scale */
  --space-0: 0;
  --space-px: 1px;
  --space-0-5: 0.125rem;
  --space-1: 0.25rem;
  --space-1-5: 0.375rem;
  --space-2: 0.5rem;
  --space-2-5: 0.625rem;
  --space-3: 0.75rem;
  --space-3-5: 0.875rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-7: 1.75rem;
  --space-8: 2rem;
  --space-9: 2.25rem;
  --space-10: 2.5rem;
  --space-11: 2.75rem;
  --space-12: 3rem;
  --space-14: 3.5rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  --space-28: 7rem;
  --space-32: 8rem;
  
  /* Enhanced Typography Scale */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;
  --font-size-7xl: 4.5rem;
  --font-size-8xl: 6rem;
  --font-size-9xl: 8rem;
  
  /* Font Weights */
  --font-weight-thin: 100;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;
  
  /* Line Heights */
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* Letter Spacing */
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0em;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;
  
  /* Enhanced Border Radius */
  --radius-none: 0;
  --radius-sm: 0.125rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;
  
  /* Enhanced Shadows */
  --shadow-none: none;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-glow: 0 0 20px rgba(255, 215, 0, 0.3);
  --shadow-glow-lg: 0 0 40px rgba(255, 215, 0, 0.4);
  --shadow-glow-pink: 0 0 20px rgba(255, 105, 180, 0.3);
  --shadow-glow-cyan: 0 0 20px rgba(0, 212, 255, 0.3);
  
  /* Enhanced Transitions */
  --transition-none: none;
  --transition-all: all 150ms ease-in-out;
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
  --transition-slower: 700ms ease-in-out;
  
  /* Professional Easing Curves */
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  --ease-back: cubic-bezier(0.68, -0.6, 0.32, 1.6);
  
  /* Z-Index Scale */
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-auto: auto;
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  
  /* Breakpoints */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
  
  /* Animation Durations */
  --duration-75: 75ms;
  --duration-100: 100ms;
  --duration-150: 150ms;
  --duration-200: 200ms;
  --duration-300: 300ms;
  --duration-500: 500ms;
  --duration-700: 700ms;
  --duration-1000: 1000ms;
}

/* Dark Theme Variables */
[data-theme="dark"] {
  --bg-primary: var(--galaxy-deep-blue);
  --bg-secondary: var(--galaxy-dark-blue);
  --bg-tertiary: var(--galaxy-medium-blue);
  --text-primary: var(--galaxy-white);
  --text-secondary: var(--galaxy-lavender);
}

/* Light Theme Variables */
[data-theme="light"] {
  --bg-primary: #f8fafc;
  --bg-secondary: #ffffff;
  --bg-tertiary: #f1f5f9;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --bg-glass: rgba(255, 255, 255, 0.8);
  --bg-glass-light: rgba(0, 0, 0, 0.1);
}
