import { useState, useRef } from 'react';
import { musicLibrary, convertSongToAudioData } from '../../data/musicLibrary';
import './MusicSelector.css';

const MusicSelector = ({ onSelectMusic, selectedMusic, onRemoveMusic }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [isPlaying, setIsPlaying] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const audioRef = useRef(null);

  const categories = [
    { id: 'all', name: 'All Songs', icon: '🎵' },
    { id: 'romantic', name: 'Romantic', icon: '💕' },
    { id: 'classical', name: 'Classical', icon: '🎼' },
    { id: 'ambient', name: 'Ambient', icon: '🌙' }
  ];

  const moods = [
    { id: 'sweet', name: 'Sweet', icon: '🍯', color: '#FFB6C1' },
    { id: 'emotional', name: 'Emotional', icon: '💖', color: '#FF69B4' },
    { id: 'joyful', name: 'Joyful', icon: '😊', color: '#FFD700' },
    { id: 'dreamy', name: 'Dreamy', icon: '☁️', color: '#E6E6FA' },
    { id: 'tender', name: 'Tender', icon: '🌸', color: '#FFC0CB' },
    { id: 'celebratory', name: 'Celebratory', icon: '🎉', color: '#FF6347' },
    { id: 'peaceful', name: 'Peaceful', icon: '🕊️', color: '#98FB98' },
    { id: 'nostalgic', name: 'Nostalgic', icon: '📜', color: '#DDA0DD' }
  ];

  const filteredSongs = musicLibrary.filter(song => {
    const matchesCategory = selectedCategory === 'all' || 
                           song.genre.toLowerCase() === selectedCategory.toLowerCase();
    const matchesSearch = song.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         song.artist.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const handleSelectSong = async (song) => {
    try {
      const audioData = await convertSongToAudioData(song);
      if (audioData) {
        onSelectMusic(audioData);
        setIsOpen(false);
      }
    } catch (error) {
      console.error('Error selecting song:', error);
    }
  };

  const handlePreviewPlay = (song) => {
    if (isPlaying === song.id) {
      audioRef.current?.pause();
      setIsPlaying(null);
    } else {
      if (audioRef.current) {
        audioRef.current.src = song.preview;
        audioRef.current.play();
        setIsPlaying(song.id);
      }
    }
  };

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getMoodInfo = (moodName) => {
    return moods.find(mood => mood.id === moodName.toLowerCase()) || 
           { icon: '🎵', color: '#FFD700' };
  };

  return (
    <div className="music-selector">
      <audio
        ref={audioRef}
        onEnded={() => setIsPlaying(null)}
        onError={() => setIsPlaying(null)}
      />

      {/* Current Selection */}
      {selectedMusic ? (
        <div className="current-music">
          <div className="music-info">
            <div className="music-icon">🎵</div>
            <div className="music-details">
              <span className="music-name">{selectedMusic.name}</span>
              <span className="music-artist">{selectedMusic.artist}</span>
            </div>
          </div>
          <div className="music-actions">
            <button
              type="button"
              onClick={() => setIsOpen(true)}
              className="btn btn-outline btn-sm"
            >
              Change
            </button>
            <button
              type="button"
              onClick={onRemoveMusic}
              className="btn btn-outline btn-sm btn-danger"
            >
              Remove
            </button>
          </div>
        </div>
      ) : (
        <div className="music-placeholder">
          <button
            type="button"
            onClick={() => setIsOpen(true)}
            className="btn btn-outline w-full"
          >
            <span className="btn-icon">🎵</span>
            Choose from Music Library
          </button>
        </div>
      )}

      {/* Music Library Modal */}
      {isOpen && (
        <div className="music-modal-overlay" onClick={() => setIsOpen(false)}>
          <div className="music-modal" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>Choose Background Music</h3>
              <button
                onClick={() => setIsOpen(false)}
                className="modal-close"
                aria-label="Close"
              >
                ×
              </button>
            </div>

            <div className="modal-content">
              {/* Search */}
              <div className="search-section">
                <div className="search-input">
                  <input
                    type="text"
                    placeholder="Search songs..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="input"
                  />
                  <span className="search-icon">🔍</span>
                </div>
              </div>

              {/* Categories */}
              <div className="categories-section">
                <div className="categories-list">
                  {categories.map(category => (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`category-btn ${selectedCategory === category.id ? 'active' : ''}`}
                    >
                      <span className="category-icon">{category.icon}</span>
                      <span className="category-name">{category.name}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Songs List */}
              <div className="songs-section">
                {filteredSongs.length > 0 ? (
                  <div className="songs-list">
                    {filteredSongs.map(song => {
                      const moodInfo = getMoodInfo(song.mood);
                      return (
                        <div key={song.id} className="song-item">
                          <div className="song-info">
                            <div className="song-main">
                              <h4 className="song-name">{song.name}</h4>
                              <p className="song-artist">{song.artist}</p>
                              <p className="song-description">{song.description}</p>
                            </div>
                            <div className="song-meta">
                              <span className="song-duration">{formatDuration(song.duration)}</span>
                              <span 
                                className="song-mood"
                                style={{ color: moodInfo.color }}
                              >
                                {moodInfo.icon} {song.mood}
                              </span>
                              <span className="song-genre">{song.genre}</span>
                            </div>
                          </div>
                          <div className="song-actions">
                            <button
                              onClick={() => handlePreviewPlay(song)}
                              className="btn btn-outline btn-sm"
                              title="Preview"
                            >
                              {isPlaying === song.id ? '⏸️' : '▶️'}
                            </button>
                            <button
                              onClick={() => handleSelectSong(song)}
                              className="btn btn-primary btn-sm"
                            >
                              Select
                            </button>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="no-songs">
                    <p>No songs found matching your criteria</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MusicSelector;
