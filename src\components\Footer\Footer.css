/* Footer Styles */
.footer {
  background: linear-gradient(135deg, var(--galaxy-deep-blue), var(--galaxy-dark-blue));
  border-top: 1px solid rgba(255, 215, 0, 0.2);
  position: relative;
  overflow: hidden;
  margin-top: var(--space-16);
}

.footer-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.floating-heart {
  position: absolute;
  font-size: var(--font-size-lg);
  color: rgba(255, 105, 180, 0.3);
  animation: floatUp 12s linear infinite;
  pointer-events: none;
}

@keyframes floatUp {
  0% {
    transform: translateY(100%) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

.footer-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-12) var(--space-4) var(--space-6);
}

.footer-main {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr;
  gap: var(--space-8);
  margin-bottom: var(--space-8);
}

.footer-section h4 {
  color: var(--galaxy-gold);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-4);
  font-family: 'Dancing Script', cursive;
}

/* Brand Section */
.footer-brand {
  max-width: 400px;
}

.brand-logo {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.brand-icon {
  font-size: var(--font-size-3xl);
  animation: heartPulse 2s ease-in-out infinite;
}

.brand-name {
  font-family: 'Dancing Script', cursive;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(135deg, var(--galaxy-gold), var(--galaxy-pink));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.brand-description {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-4);
}

.love-quote {
  background: rgba(255, 255, 255, 0.05);
  border-left: 3px solid var(--galaxy-gold);
  padding: var(--space-3);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
}

.love-quote blockquote {
  font-style: italic;
  color: var(--galaxy-lavender);
  margin: 0;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
}

/* Links Section */
.footer-links ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: var(--space-2);
}

.footer-link {
  color: var(--text-secondary);
  text-decoration: none;
  transition: all var(--transition-normal);
  display: inline-block;
  position: relative;
}

.footer-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--galaxy-gold);
  transition: width var(--transition-normal);
}

.footer-link:hover {
  color: var(--galaxy-gold);
  transform: translateX(5px);
}

.footer-link:hover::after {
  width: 100%;
}

/* Social Section */
.social-links {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.social-link {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid transparent;
}

.social-link:hover {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--social-color);
  transform: translateX(5px);
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.1);
}

.social-icon {
  font-size: var(--font-size-lg);
}

.social-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

/* Newsletter Section */
.footer-newsletter p {
  color: var(--text-secondary);
  margin-bottom: var(--space-4);
  font-size: var(--font-size-sm);
}

.newsletter-form {
  margin-bottom: var(--space-3);
}

.input-group {
  display: flex;
  border-radius: var(--radius-lg);
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all var(--transition-normal);
}

.input-group:focus-within {
  border-color: var(--galaxy-gold);
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.2);
}

.newsletter-input {
  flex: 1;
  padding: var(--space-3);
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  outline: none;
}

.newsletter-input::placeholder {
  color: var(--text-muted);
}

.newsletter-btn {
  padding: var(--space-3);
  background: linear-gradient(135deg, var(--galaxy-gold), var(--galaxy-pink));
  border: none;
  color: var(--galaxy-deep-blue);
  font-size: var(--font-size-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  min-width: 50px;
}

.newsletter-btn:hover {
  background: linear-gradient(135deg, var(--galaxy-pink), var(--galaxy-gold));
  transform: scale(1.05);
}

.subscribe-success {
  color: var(--color-success);
  font-size: var(--font-size-sm);
  margin-top: var(--space-2);
  animation: fadeInUp 0.5s ease;
}

/* Footer Bottom */
.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: var(--space-6);
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-4);
}

.copyright p {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  margin: 0;
}

.footer-stats {
  display: flex;
  gap: var(--space-6);
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--galaxy-gold);
  line-height: 1;
}

.stat-label {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
}

/* Animations */
@keyframes heartPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .footer-main {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
  
  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
  }
  
  .footer-stats {
    justify-content: center;
  }
  
  .social-links {
    flex-direction: row;
    flex-wrap: wrap;
  }
  
  .social-link {
    flex: 1;
    min-width: 120px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .footer-content {
    padding: var(--space-8) var(--space-3) var(--space-4);
  }
  
  .footer-stats {
    flex-direction: column;
    gap: var(--space-3);
  }
  
  .social-links {
    flex-direction: column;
  }
  
  .brand-logo {
    justify-content: center;
  }
  
  .footer-section {
    text-align: center;
  }
}
