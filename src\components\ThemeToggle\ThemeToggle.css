/* Theme Toggle Component */
.theme-toggle {
  position: fixed;
  top: var(--space-4);
  right: var(--space-4);
  z-index: var(--z-fixed);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--bg-glass);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full);
  backdrop-filter: blur(20px);
  transition: all var(--transition-normal);
  cursor: pointer;
  user-select: none;
  box-shadow: var(--shadow-lg);
}

.theme-toggle:hover {
  background: var(--bg-glass-light);
  transform: scale(1.05);
  box-shadow: var(--shadow-glow);
}

.theme-toggle:active {
  transform: scale(0.95);
}

.toggle-track {
  position: relative;
  width: 48px;
  height: 24px;
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all var(--transition-normal);
}

.toggle-thumb {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 18px;
  height: 18px;
  background: linear-gradient(135deg, var(--galaxy-gold), var(--galaxy-light-gold));
  border-radius: 50%;
  transition: all var(--transition-normal) var(--ease-bounce);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-md);
}

[data-theme="light"] .toggle-thumb {
  transform: translateX(24px);
  background: linear-gradient(135deg, var(--galaxy-pink), var(--galaxy-light-pink));
}

.toggle-icon {
  width: 12px;
  height: 12px;
  color: var(--galaxy-deep-blue);
  transition: all var(--transition-normal);
}

.toggle-icon svg {
  width: 100%;
  height: 100%;
  stroke-width: 2.5;
}

.toggle-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  min-width: 32px;
  text-align: center;
}

/* Animation states */
.theme-toggle.animating .toggle-thumb {
  animation: thumbBounce 0.6s var(--ease-bounce);
}

.theme-toggle.animating .toggle-icon {
  animation: iconSpin 0.6s var(--ease-out);
}

@keyframes thumbBounce {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

@keyframes iconSpin {
  0% { transform: rotate(0deg); opacity: 1; }
  50% { transform: rotate(180deg); opacity: 0.5; }
  100% { transform: rotate(360deg); opacity: 1; }
}

/* Theme transition */
:root.theme-transitioning,
:root.theme-transitioning * {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
}

/* Responsive */
@media (max-width: 768px) {
  .theme-toggle {
    top: var(--space-3);
    right: var(--space-3);
    padding: var(--space-1-5) var(--space-2);
  }
  
  .toggle-track {
    width: 40px;
    height: 20px;
  }
  
  .toggle-thumb {
    width: 16px;
    height: 16px;
  }
  
  [data-theme="light"] .toggle-thumb {
    transform: translateX(20px);
  }
  
  .toggle-icon {
    width: 10px;
    height: 10px;
  }
  
  .toggle-label {
    font-size: var(--font-size-xs);
    min-width: 28px;
  }
}
