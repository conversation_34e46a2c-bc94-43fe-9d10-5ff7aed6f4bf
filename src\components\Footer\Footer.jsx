import { useState } from 'react';
import './Footer.css';

const Footer = () => {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);

  const handleSubscribe = (e) => {
    e.preventDefault();
    if (email) {
      setIsSubscribed(true);
      setEmail('');
      setTimeout(() => setIsSubscribed(false), 3000);
    }
  };

  const socialLinks = [
    { name: 'Facebook', icon: '📘', url: '#', color: '#1877f2' },
    { name: 'Instagram', icon: '📷', url: '#', color: '#e4405f' },
    { name: 'Twitter', icon: '🐦', url: '#', color: '#1da1f2' },
    { name: 'TikTok', icon: '🎵', url: '#', color: '#000000' },
    { name: 'YouTube', icon: '📺', url: '#', color: '#ff0000' },
  ];

  const quickLinks = [
    { name: 'Create Story', path: '/' },
    { name: 'Gallery', path: '/gallery' },
    { name: 'About Us', path: '/about' },
    { name: 'Privacy Policy', path: '/privacy' },
    { name: 'Terms of Service', path: '/terms' },
  ];

  const loveQuotes = [
    "Love is not about how many days, months, or years you have been together. Love is about how much you love each other every single day.",
    "In all the world, there is no heart for me like yours. In all the world, there is no love for you like mine.",
    "You are my today and all of my tomorrows.",
    "Every love story is beautiful, but ours is my favorite."
  ];

  const [currentQuote] = useState(loveQuotes[Math.floor(Math.random() * loveQuotes.length)]);

  return (
    <footer className="footer">
      <div className="footer-background">
        {/* Floating hearts */}
        {[...Array(20)].map((_, index) => (
          <div
            key={index}
            className="floating-heart"
            style={{
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${8 + Math.random() * 4}s`
            }}
          >
            💕
          </div>
        ))}
      </div>

      <div className="footer-content">
        {/* Main Footer */}
        <div className="footer-main">
          <div className="footer-section footer-brand">
            <div className="brand-logo">
              <div className="brand-icon">💖</div>
              <h3 className="brand-name">Love Story</h3>
            </div>
            <p className="brand-description">
              Create beautiful, personalized romantic experiences that capture your unique love story forever.
            </p>
            <div className="love-quote">
              <blockquote>"{currentQuote}"</blockquote>
            </div>
          </div>

          <div className="footer-section footer-links">
            <h4>Quick Links</h4>
            <ul>
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <a href={link.path} className="footer-link">
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          <div className="footer-section footer-social">
            <h4>Follow Our Love Journey</h4>
            <div className="social-links">
              {socialLinks.map((social) => (
                <a
                  key={social.name}
                  href={social.url}
                  className="social-link"
                  style={{ '--social-color': social.color }}
                  title={social.name}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <span className="social-icon">{social.icon}</span>
                  <span className="social-name">{social.name}</span>
                </a>
              ))}
            </div>
          </div>

          <div className="footer-section footer-newsletter">
            <h4>Love Updates</h4>
            <p>Get romantic inspiration and updates delivered to your heart.</p>
            <form onSubmit={handleSubscribe} className="newsletter-form">
              <div className="input-group">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  className="newsletter-input"
                  required
                />
                <button type="submit" className="newsletter-btn">
                  {isSubscribed ? '💖' : '📧'}
                </button>
              </div>
              {isSubscribed && (
                <div className="subscribe-success">
                  Thank you for joining our love community! 💕
                </div>
              )}
            </form>
          </div>
        </div>

        {/* Footer Bottom */}
        <div className="footer-bottom">
          <div className="footer-bottom-content">
            <div className="copyright">
              <p>&copy; 2024 Love Story. Made with 💖 for couples everywhere.</p>
            </div>
            <div className="footer-stats">
              <div className="stat">
                <span className="stat-number">10,000+</span>
                <span className="stat-label">Love Stories Created</span>
              </div>
              <div className="stat">
                <span className="stat-number">50,000+</span>
                <span className="stat-label">Happy Couples</span>
              </div>
              <div className="stat">
                <span className="stat-number">∞</span>
                <span className="stat-label">Endless Love</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
