// Local storage utilities for romantic pages

const STORAGE_KEY = 'romantic_pages';
const MAX_STORAGE_SIZE = 4 * 1024 * 1024; // 4MB limit (localStorage is usually 5-10MB)
const MAX_IMAGE_SIZE = 500 * 1024; // 500KB per image
const MAX_AUDIO_SIZE = 2 * 1024 * 1024; // 2MB for audio

// Compress and optimize data before storage
const optimizeDataForStorage = async (data) => {
  const optimizedData = { ...data };

  // Compress images
  if (optimizedData.images && optimizedData.images.length > 0) {
    optimizedData.images = await Promise.all(
      optimizedData.images.map(async (image) => {
        if (image.base64 && image.base64.length > MAX_IMAGE_SIZE) {
          // Create a compressed version
          const compressedBase64 = await compressBase64Image(image.base64, 0.7, 800);
          return {
            ...image,
            base64: compressedBase64,
            compressed: true
          };
        }
        return image;
      })
    );
  }

  // Compress audio if too large
  if (optimizedData.audio && optimizedData.audio.base64) {
    if (optimizedData.audio.base64.length > MAX_AUDIO_SIZE) {
      // For audio, we'll store a reference and show a message
      optimizedData.audio = {
        ...optimizedData.audio,
        base64: null,
        tooLarge: true,
        originalSize: optimizedData.audio.base64.length
      };
    }
  }

  return optimizedData;
};

// Compress base64 image
const compressBase64Image = (base64, quality = 0.7, maxWidth = 800) => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      // Calculate new dimensions
      let { width, height } = img;
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }

      canvas.width = width;
      canvas.height = height;

      // Draw and compress
      ctx.drawImage(img, 0, 0, width, height);
      const compressedBase64 = canvas.toDataURL('image/jpeg', quality);
      resolve(compressedBase64);
    };
    img.src = base64;
  });
};

// Check storage size
const getStorageSize = () => {
  let total = 0;
  for (let key in localStorage) {
    if (localStorage.hasOwnProperty(key)) {
      total += localStorage[key].length + key.length;
    }
  }
  return total;
};

// Clean up old pages to free space
const cleanupForSpace = () => {
  try {
    const pages = getRomanticPages();
    const pageEntries = Object.entries(pages);

    // Sort by creation date (oldest first)
    pageEntries.sort((a, b) => new Date(a[1].createdAt) - new Date(b[1].createdAt));

    // Remove oldest pages until we have enough space
    const cleanedPages = {};
    let currentSize = 0;

    // Keep newest pages that fit within size limit
    for (let i = pageEntries.length - 1; i >= 0; i--) {
      const [id, data] = pageEntries[i];
      const entrySize = JSON.stringify(data).length;

      if (currentSize + entrySize < MAX_STORAGE_SIZE * 0.8) { // Use 80% of limit
        cleanedPages[id] = data;
        currentSize += entrySize;
      }
    }

    localStorage.setItem(STORAGE_KEY, JSON.stringify(cleanedPages));
    return Object.keys(cleanedPages).length;
  } catch (error) {
    console.error('Error cleaning up storage:', error);
    return 0;
  }
};

export const saveRomanticPage = async (id, data) => {
  try {
    // First, optimize the data
    const optimizedData = await optimizeDataForStorage(data);

    // Add metadata
    const pageData = {
      ...optimizedData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Check if we need to clean up space
    const currentSize = getStorageSize();
    if (currentSize > MAX_STORAGE_SIZE * 0.7) { // Clean up when 70% full
      cleanupForSpace();
    }

    const existingPages = getRomanticPages();
    existingPages[id] = pageData;

    // Try to save
    const dataString = JSON.stringify(existingPages);
    if (dataString.length > MAX_STORAGE_SIZE) {
      // If still too large, clean up more aggressively
      const remainingPages = cleanupForSpace();
      if (remainingPages === 0) {
        // If we had to remove all pages, start fresh
        const freshPages = { [id]: pageData };
        localStorage.setItem(STORAGE_KEY, JSON.stringify(freshPages));
      } else {
        // Try again with cleaned storage
        const cleanedPages = getRomanticPages();
        cleanedPages[id] = pageData;
        localStorage.setItem(STORAGE_KEY, JSON.stringify(cleanedPages));
      }
    } else {
      localStorage.setItem(STORAGE_KEY, dataString);
    }

    return { success: true, optimized: true };
  } catch (error) {
    console.error('Error saving romantic page:', error);

    // If quota exceeded, try emergency cleanup
    if (error.name === 'QuotaExceededError') {
      try {
        // Emergency cleanup - remove all old pages
        localStorage.removeItem(STORAGE_KEY);

        // Try to save just this page with maximum compression
        const emergencyData = await optimizeDataForStorage(data);

        // Further compress images if needed
        if (emergencyData.images) {
          emergencyData.images = await Promise.all(
            emergencyData.images.map(async (image) => {
              if (image.base64) {
                const ultraCompressed = await compressBase64Image(image.base64, 0.5, 400);
                return {
                  ...image,
                  base64: ultraCompressed,
                  ultraCompressed: true
                };
              }
              return image;
            })
          );
        }

        // Remove audio if still too large
        if (emergencyData.audio) {
          emergencyData.audio = {
            name: emergencyData.audio.name,
            size: emergencyData.audio.size,
            removed: true,
            reason: 'Storage space optimization'
          };
        }

        const emergencyPages = {
          [id]: {
            ...emergencyData,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            emergency: true
          }
        };

        localStorage.setItem(STORAGE_KEY, JSON.stringify(emergencyPages));
        return { success: true, emergency: true, message: 'Saved with maximum compression due to storage limits' };
      } catch (emergencyError) {
        console.error('Emergency save failed:', emergencyError);
        return { success: false, error: 'Storage quota exceeded. Please try with fewer or smaller images.' };
      }
    }

    return { success: false, error: error.message };
  }
};

export const getRomanticPage = (id) => {
  try {
    const pages = getRomanticPages();
    return pages[id] || null;
  } catch (error) {
    console.error('Error getting romantic page:', error);
    return null;
  }
};

export const getRomanticPages = () => {
  try {
    const pages = localStorage.getItem(STORAGE_KEY);
    return pages ? JSON.parse(pages) : {};
  } catch (error) {
    console.error('Error getting romantic pages:', error);
    return {};
  }
};

export const deleteRomanticPage = (id) => {
  try {
    const pages = getRomanticPages();
    delete pages[id];
    localStorage.setItem(STORAGE_KEY, JSON.stringify(pages));
    return true;
  } catch (error) {
    console.error('Error deleting romantic page:', error);
    return false;
  }
};

export const getAllRomanticPages = () => {
  try {
    const pages = getRomanticPages();
    return Object.entries(pages).map(([id, data]) => ({
      id,
      ...data
    }));
  } catch (error) {
    console.error('Error getting all romantic pages:', error);
    return [];
  }
};

// Clean up old pages (older than 30 days)
export const cleanupOldPages = () => {
  try {
    const pages = getRomanticPages();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const cleanedPages = {};
    Object.entries(pages).forEach(([id, data]) => {
      const createdAt = new Date(data.createdAt);
      if (createdAt > thirtyDaysAgo) {
        cleanedPages[id] = data;
      }
    });
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(cleanedPages));
    return true;
  } catch (error) {
    console.error('Error cleaning up old pages:', error);
    return false;
  }
};

// Check if storage is available
export const isStorageAvailable = () => {
  try {
    const test = '__storage_test__';
    localStorage.setItem(test, test);
    localStorage.removeItem(test);
    return true;
  } catch (error) {
    return false;
  }
};
