/* Loading Screen Styles */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, var(--galaxy-deep-blue), var(--galaxy-dark-blue));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  opacity: 1;
  visibility: visible;
  transition: all 0.5s var(--ease-out);
}

.loading-screen.complete {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

.loading-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.star {
  position: absolute;
  width: 2px;
  height: 2px;
  background: var(--galaxy-gold);
  border-radius: 50%;
  animation: twinkle 2s ease-in-out infinite;
  box-shadow: 0 0 6px var(--galaxy-gold);
}

@keyframes twinkle {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.5); }
}

.loading-content {
  text-align: center;
  position: relative;
  z-index: 1;
}

.loading-logo {
  margin-bottom: var(--space-8);
  animation: logoFloat 3s ease-in-out infinite;
}

.logo-heart {
  font-size: 4rem;
  margin-bottom: var(--space-2);
  animation: heartBeat 1.5s ease-in-out infinite;
}

.logo-text {
  font-family: 'Dancing Script', cursive;
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(135deg, var(--galaxy-gold), var(--galaxy-pink));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.progress-container {
  position: relative;
  margin: var(--space-8) auto;
  width: 120px;
  height: 120px;
}

.progress-circle {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
  filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.3));
}

.progress-bar {
  transition: stroke-dashoffset 0.3s var(--ease-out);
  animation: progressGlow 2s ease-in-out infinite;
}

.progress-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.progress-percentage {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--galaxy-gold);
  margin-bottom: var(--space-1);
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.progress-hearts {
  display: flex;
  justify-content: center;
  gap: var(--space-1);
}

.progress-hearts span {
  font-size: var(--font-size-sm);
  animation: heartFloat 2s ease-in-out infinite;
}

.heart-1 { animation-delay: 0s; }
.heart-2 { animation-delay: 0.3s; }
.heart-3 { animation-delay: 0.6s; }

.loading-text {
  margin-top: var(--space-6);
}

.loading-message {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
  min-height: 1.5em;
  animation: textFade 0.5s ease-in-out;
}

.loading-dots {
  display: flex;
  justify-content: center;
  gap: var(--space-1);
}

.loading-dots span {
  width: 8px;
  height: 8px;
  background: var(--galaxy-pink);
  border-radius: 50%;
  animation: dotBounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-element {
  position: absolute;
  font-size: var(--font-size-xl);
  animation: floatUp 4s ease-in-out infinite;
  opacity: 0.7;
}

/* Animations */
@keyframes logoFloat {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes heartBeat {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes progressGlow {
  0%, 100% { filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.3)); }
  50% { filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.6)); }
}

@keyframes heartFloat {
  0%, 100% { transform: translateY(0) scale(1); }
  50% { transform: translateY(-5px) scale(1.1); }
}

@keyframes textFade {
  0% { opacity: 0; transform: translateY(10px); }
  100% { opacity: 1; transform: translateY(0); }
}

@keyframes dotBounce {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

@keyframes floatUp {
  0% { 
    transform: translateY(100vh) rotate(0deg); 
    opacity: 0; 
  }
  10% { 
    opacity: 0.7; 
  }
  90% { 
    opacity: 0.7; 
  }
  100% { 
    transform: translateY(-100px) rotate(360deg); 
    opacity: 0; 
  }
}

/* Responsive */
@media (max-width: 768px) {
  .logo-heart {
    font-size: 3rem;
  }
  
  .logo-text {
    font-size: var(--font-size-3xl);
  }
  
  .progress-container {
    width: 100px;
    height: 100px;
  }
  
  .progress-percentage {
    font-size: var(--font-size-lg);
  }
  
  .loading-message {
    font-size: var(--font-size-base);
  }
}
